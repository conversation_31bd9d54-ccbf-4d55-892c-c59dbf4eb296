/**
 * PhantomSDR-Plus Waterfall Component
 * Apple-inspired React component with production-grade quality
 */

'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import classNames from 'classnames';
import { useWaterfall } from '@/hooks/useWaterfall';
import { WaterfallProps, WaterfallDisplayParams, WaterfallRenderOptions } from '@/types/waterfall';
import { WaterfallControls } from './WaterfallControls';
import { WaterfallStats } from './WaterfallStats';
import styles from './Waterfall.module.css';

/**
 * Main Waterfall Component
 * Provides a complete waterfall display with controls and statistics
 */
export const Waterfall: React.FC<WaterfallProps> = ({
  websocketUrl,
  width = 1024,
  height = 400,
  displayParams = {},
  renderOptions = {},
  eventHandlers = {},
  className,
  style,
}) => {
  // State for UI controls
  const [showControls, setShowControls] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);

  // Container ref for fullscreen
  const containerRef = useRef<HTMLDivElement>(null);

  // Merge display parameters
  const finalDisplayParams: Partial<WaterfallDisplayParams> = {
    width,
    height,
    ...displayParams,
  };

  // Merge render options
  const finalRenderOptions: Partial<WaterfallRenderOptions> = {
    smoothing: true,
    interpolation: 'linear',
    refreshRate: 60,
    bufferSize: 1000,
    enableGPU: true,
    ...renderOptions,
  };

  // Use waterfall hook
  const {
    isConnected,
    isConnecting,
    config,
    error,
    stats,
    canvasRef,
    connect,
    disconnect,
    setWindow,
    setUserId,
    setMute,
    setBrightness,
    setContrast,
    setColorMap,
    updateDisplayParams,
    updateRenderOptions,
    clear,
    getRenderStats,
  } = useWaterfall({
    websocketUrl,
    displayParams: finalDisplayParams,
    renderOptions: finalRenderOptions,
    autoConnect: true,
    reconnectOnError: true,
  });

  // Forward events to external handlers
  useEffect(() => {
    if (config && eventHandlers.onConnect) {
      eventHandlers.onConnect(config);
    }
  }, [config, eventHandlers.onConnect]);

  useEffect(() => {
    if (error && eventHandlers.onError) {
      eventHandlers.onError(error);
    }
  }, [error, eventHandlers.onError]);

  useEffect(() => {
    if (!isConnected && eventHandlers.onDisconnect) {
      eventHandlers.onDisconnect();
    }
  }, [isConnected, eventHandlers.onDisconnect]);

  // Mouse event handlers for frequency selection
  const handleMouseDown = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !config) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setIsDragging(true);
    setDragStart({ x, y });

    // Calculate frequency from mouse position
    const frequency = calculateFrequencyFromX(x, canvasRef.current.width, config);
    
    if (eventHandlers.onFrequencyChange) {
      eventHandlers.onFrequencyChange(frequency);
    }
  }, [config, eventHandlers.onFrequencyChange]);

  const handleMouseMove = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !dragStart || !canvasRef.current || !config) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    
    // Calculate frequency range for window selection
    const startFreq = calculateFrequencyFromX(dragStart.x, canvasRef.current.width, config);
    const endFreq = calculateFrequencyFromX(x, canvasRef.current.width, config);
    
    const left = Math.min(startFreq, endFreq);
    const right = Math.max(startFreq, endFreq);
    const center = (left + right) / 2;
    
    // Update window selection
    setWindow(left, right, center);
  }, [isDragging, dragStart, config, setWindow]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setDragStart(null);
  }, []);

  // Keyboard event handlers
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'f':
      case 'F':
        toggleFullscreen();
        break;
      case 'c':
      case 'C':
        setShowControls(!showControls);
        break;
      case 's':
      case 'S':
        setShowStats(!showStats);
        break;
      case 'r':
      case 'R':
        clear();
        break;
      case 'Escape':
        if (isFullscreen) {
          exitFullscreen();
        }
        break;
    }
  }, [showControls, showStats, isFullscreen, clear]);

  // Fullscreen functionality
  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!isFullscreen) {
      containerRef.current.requestFullscreen?.();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen?.();
      setIsFullscreen(false);
    }
  }, [isFullscreen]);

  const exitFullscreen = useCallback(() => {
    document.exitFullscreen?.();
    setIsFullscreen(false);
  }, []);

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Calculate frequency from X coordinate
  const calculateFrequencyFromX = (x: number, canvasWidth: number, config: any): number => {
    const ratio = x / canvasWidth;
    const totalBandwidth = config.total_bandwidth;
    return config.basefreq - totalBandwidth / 2 + ratio * totalBandwidth;
  };

  // Connection status indicator
  const renderConnectionStatus = () => {
    const statusClass = classNames(styles.connectionStatus, {
      [styles.connected]: isConnected,
      [styles.connecting]: isConnecting,
      [styles.disconnected]: !isConnected && !isConnecting,
      [styles.error]: !!error,
    });

    let statusText = 'Disconnected';
    if (isConnecting) statusText = 'Connecting...';
    else if (isConnected) statusText = 'Connected';
    else if (error) statusText = `Error: ${error.message}`;

    return (
      <div className={statusClass}>
        <div className={styles.statusIndicator} />
        <span className={styles.statusText}>{statusText}</span>
      </div>
    );
  };

  // Render loading state
  if (isConnecting) {
    return (
      <div className={classNames(styles.container, styles.loading, className)} style={style}>
        <div className={styles.loadingSpinner} />
        <p className={styles.loadingText}>Connecting to PhantomSDR-Plus...</p>
      </div>
    );
  }

  // Render error state
  if (error && !isConnected) {
    return (
      <div className={classNames(styles.container, styles.error, className)} style={style}>
        <div className={styles.errorIcon}>⚠️</div>
        <h3 className={styles.errorTitle}>Connection Failed</h3>
        <p className={styles.errorMessage}>{error.message}</p>
        <button className={styles.retryButton} onClick={connect}>
          Retry Connection
        </button>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={classNames(styles.container, {
        [styles.fullscreen]: isFullscreen,
        [styles.dragging]: isDragging,
      }, className)}
      style={style}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.title}>
          <h2>PhantomSDR-Plus Waterfall</h2>
          {config && (
            <span className={styles.subtitle}>
              {(config.basefreq / 1e6).toFixed(1)} MHz • {(config.total_bandwidth / 1e6).toFixed(1)} MHz span
            </span>
          )}
        </div>
        
        <div className={styles.headerControls}>
          {renderConnectionStatus()}
          
          <button
            className={styles.controlButton}
            onClick={() => setShowControls(!showControls)}
            title="Toggle Controls (C)"
          >
            ⚙️
          </button>
          
          <button
            className={styles.controlButton}
            onClick={() => setShowStats(!showStats)}
            title="Toggle Statistics (S)"
          >
            📊
          </button>
          
          <button
            className={styles.controlButton}
            onClick={toggleFullscreen}
            title="Toggle Fullscreen (F)"
          >
            {isFullscreen ? '🗗' : '🗖'}
          </button>
        </div>
      </div>

      {/* Main waterfall canvas */}
      <div className={styles.canvasContainer}>
        <canvas
          ref={canvasRef}
          className={styles.canvas}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        />
        
        {/* Frequency scale overlay */}
        {config && (
          <div className={styles.frequencyScale}>
            {/* This would render frequency markers */}
          </div>
        )}
      </div>

      {/* Controls panel */}
      {showControls && (
        <WaterfallControls
          config={config}
          onBrightnessChange={setBrightness}
          onContrastChange={setContrast}
          onColorMapChange={setColorMap}
          onDisplayParamsChange={updateDisplayParams}
          onRenderOptionsChange={updateRenderOptions}
          onClear={clear}
        />
      )}

      {/* Statistics panel */}
      {showStats && (
        <WaterfallStats
          stats={stats}
          renderStats={getRenderStats()}
          config={config}
        />
      )}

      {/* Keyboard shortcuts help */}
      <div className={styles.shortcuts}>
        <small>
          F: Fullscreen • C: Controls • S: Stats • R: Clear • Click & drag: Select frequency
        </small>
      </div>
    </div>
  );
};

export default Waterfall;
