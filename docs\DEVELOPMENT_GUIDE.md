# PhantomSDR-Plus Development Guide

This guide provides detailed instructions for developers who want to contribute to or extend PhantomSDR-Plus.

## Table of Contents

1. [Development Environment Setup](#1-development-environment-setup)
2. [Project Structure](#2-project-structure)
3. [Adding New Features](#3-adding-new-features)
4. [Backend Development](#4-backend-development)
5. [Frontend Development](#5-frontend-development)
6. [Testing and Debugging](#6-testing-and-debugging)
7. [Contributing Guidelines](#7-contributing-guidelines)

---

## 1. Development Environment Setup

### Required Tools

```bash
# Core development tools
sudo apt install -y \
    build-essential \
    git \
    cmake \
    meson \
    ninja-build \
    ccache \
    gdb \
    valgrind \
    clang-format \
    clang-tidy

# Frontend development
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
npm install -g pnpm
```

### IDE Setup

#### Visual Studio Code

Create `.vscode/c_cpp_properties.json`:

```json
{
  "configurations": [{
    "name": "Linux",
    "includePath": [
      "${workspaceFolder}/src/**",
      "${workspaceFolder}/subprojects/**",
      "/usr/include"
    ],
    "defines": ["HAS_LIQUID", "CUFFT"],
    "compilerPath": "/usr/bin/g++",
    "cStandard": "c17",
    "cppStandard": "c++23",
    "intelliSenseMode": "linux-gcc-x64"
  }]
}
```

Create `.vscode/launch.json` for debugging:

```json
{
  "version": "0.2.0",
  "configurations": [{
    "name": "Debug spectrumserver",
    "type": "cppdbg",
    "request": "launch",
    "program": "${workspaceFolder}/build/spectrumserver",
    "args": ["--config", "config.toml"],
    "stopAtEntry": false,
    "cwd": "${workspaceFolder}",
    "environment": [],
    "externalConsole": false,
    "MIMode": "gdb"
  }]
}
```

### Building for Development

```bash
# Debug build with sanitizers
meson setup builddir --buildtype=debug \
    -Db_sanitize=address,undefined \
    -Db_coverage=true

# Compile
meson compile -C builddir

# Run tests
meson test -C builddir
```

---

## 2. Project Structure

### Backend Structure

```
src/
├── Core Components
│   ├── spectrumserver.cpp/h    # Main server class
│   ├── client.cpp/h            # Base client classes
│   └── websocket.cpp/h         # WebSocket handling
├── Processing
│   ├── fft.cpp/h              # FFT processing
│   ├── fft_impl.cpp           # FFT implementations
│   ├── fft_cuda.cu            # CUDA FFT
│   └── fft_mkl.cpp            # Intel MKL FFT
├── Data Streams
│   ├── waterfall.cpp/h        # Waterfall generation
│   ├── signal.cpp/h           # Signal processing
│   ├── audio.cpp/h            # Audio encoding
│   └── events.cpp/h           # Event streaming
├── Features
│   ├── chat.cpp/h             # Chat system
│   ├── http.cpp               # HTTP handler
│   └── compression.cpp/h      # Data compression
├── Utilities
│   ├── samplereader.cpp/h     # Sample input
│   ├── utils.cpp/h            # General utilities
│   └── utils/
│       ├── dsp.cpp/h          # DSP functions
│       └── audioprocessing.cpp/h
└── Compression
    ├── waterfallcompression.cpp/h
    └── compression.cpp/h
```

### Frontend Structure

```
frontend/
├── src/
│   ├── App.svelte              # Main component
│   ├── main.js                 # Entry point
│   ├── Core Modules
│   │   ├── audio.js            # Audio handling
│   │   ├── waterfall.js        # Waterfall display
│   │   ├── events.js           # Event handling
│   │   └── fft.js              # Spectrum display
│   ├── lib/
│   │   ├── backend.js          # Server communication
│   │   ├── colormaps.js        # Color schemes
│   │   ├── storage.js          # Local storage
│   │   └── wrappers.js         # WASM wrappers
│   ├── modules/                # WebAssembly modules
│   │   ├── phantomsdrdsp.js
│   │   ├── ft8.js
│   │   └── *.wasm
│   └── assets/                 # Static resources
└── dist/                       # Built output
```

---

## 3. Adding New Features

### Adding a New WebSocket Endpoint

#### Step 1: Define the endpoint handler

In [`src/websocket.cpp`](../src/websocket.cpp), add to `on_open()`:

```cpp
void broadcast_server::on_open(connection_hdl hdl) {
    server::connection_ptr con = m_server.get_con_from_hdl(hdl);
    std::string path = con->get_resource();

    // Add your new endpoint
    if (path == "/myfeature") {
        on_open_myfeature(hdl);
    }
    // ... existing endpoints
}
```

#### Step 2: Create the client class

Create `src/myfeature.h`:

```cpp
#ifndef MYFEATURE_H
#define MYFEATURE_H

#include "client.h"

class MyFeatureClient : public Client {
public:
    MyFeatureClient(connection_hdl hdl, PacketSender &sender);
    
    // Override message handlers
    virtual void on_window_message(int l, std::optional<double> &m, 
                                  int r, std::optional<int> &level) override;
    
    // Custom functionality
    void send_data(const std::vector<float> &data);
    void on_close();
    
private:
    // Add member variables
    std::vector<float> buffer;
};

#endif
```

Create `src/myfeature.cpp`:

```cpp
#include "myfeature.h"
#include "glaze/glaze.hpp"

MyFeatureClient::MyFeatureClient(connection_hdl hdl, PacketSender &sender)
    : Client(hdl, sender, MYFEATURE) {
    // Initialize
}

void MyFeatureClient::send_data(const std::vector<float> &data) {
    // Create packet
    glz::json_t packet = {
        {"type", "data"},
        {"values", data},
        {"timestamp", frame_num}
    };
    
    // Send as JSON
    sender.send_text_packet(hdl, glz::write_json(packet));
}

void MyFeatureClient::on_close() {
    // Cleanup
}
```

#### Step 3: Add the handler function

In [`src/spectrumserver.h`](../src/spectrumserver.h):

```cpp
void on_open_myfeature(connection_hdl hdl);
```

In [`src/spectrumserver.cpp`](../src/spectrumserver.cpp):

```cpp
void broadcast_server::on_open_myfeature(connection_hdl hdl) {
    // Send initial configuration
    send_basic_info(hdl);
    
    // Create client instance
    auto client = std::make_shared<MyFeatureClient>(hdl, *this);
    
    // Store client (if needed)
    myfeature_clients[hdl] = client;
    
    // Set handlers
    server::connection_ptr con = m_server.get_con_from_hdl(hdl);
    con->set_close_handler(std::bind(&MyFeatureClient::on_close, client));
    con->set_message_handler(std::bind(
        &broadcast_server::on_message, this, std::placeholders::_1,
        std::placeholders::_2, std::static_pointer_cast<Client>(client)));
}
```

### Adding a New Demodulation Mode

#### Step 1: Add the enum value

In [`src/client.h`](../src/client.h):

```cpp
enum demodulation_mode { USB, LSB, AM, FM, CW, DIGITAL };
```

#### Step 2: Implement demodulation

In [`src/signal.cpp`](../src/signal.cpp), modify `send_audio()`:

```cpp
void AudioClient::send_audio(std::complex<float> *buf, size_t frame_num) {
    // ... existing code ...
    
    switch (demodulation) {
    case CW:
        // CW demodulation
        demodulate_cw(audio_complex_baseband.get(), 
                     audio_real.data(), 
                     audio_fft_size);
        break;
    case DIGITAL:
        // Digital mode demodulation
        demodulate_digital(audio_complex_baseband.get(), 
                          audio_real.data(), 
                          audio_fft_size);
        break;
    // ... existing modes
    }
}

void demodulate_cw(std::complex<float> *in, float *out, int size) {
    // Implement CW demodulation
    // 1. Apply narrow filter (500 Hz)
    // 2. Generate beat frequency oscillator (BFO)
    // 3. Mix with BFO
    // 4. Low-pass filter
}
```

#### Step 3: Update frontend

In [`frontend/src/audio.js`](../frontend/src/audio.js):

```javascript
setAudioDemodulation(demodulation) {
    this.demodulation = demodulation;
    
    // Handle special modes
    if (demodulation === "CW") {
        this.setLowpass(500);  // Narrow filter for CW
    }
    
    this.updateFilters();
    this.audioSocket.send(JSON.stringify({
        cmd: 'demodulation',
        demodulation: demodulation
    }));
}
```

### Adding a New Compression Format

#### Step 1: Add compression enum

In [`src/client.h`](../src/client.h):

```cpp
enum waterfall_compressor { WATERFALL_ZSTD, WATERFALL_AV1, WATERFALL_BROTLI };
```

#### Step 2: Implement encoder

In [`src/waterfallcompression.h`](../src/waterfallcompression.h):

```cpp
class BrotliWaterfallEncoder : public WaterfallEncoder {
public:
    BrotliWaterfallEncoder();
    virtual std::vector<uint8_t> encode(const int8_t* data, 
                                       size_t size) override;
    virtual ~BrotliWaterfallEncoder();
    
private:
    void* encoder_state;
};
```

#### Step 3: Add to factory

In [`src/waterfallcompression.cpp`](../src/waterfallcompression.cpp):

```cpp
std::unique_ptr<WaterfallEncoder> 
create_waterfall_encoder(waterfall_compressor type) {
    switch (type) {
    case WATERFALL_BROTLI:
        return std::make_unique<BrotliWaterfallEncoder>();
    // ... existing cases
    }
}
```

---

## 4. Backend Development

### Message Protocol Extension

To add new message types, modify the variant in [`src/client.cpp`](../src/client.cpp):

```cpp
// Define new command structure
struct mycommand_cmd {
    int parameter1;
    std::string parameter2;
};

// Add glaze metadata
template <>
struct glz::meta<mycommand_cmd> {
    using T = mycommand_cmd;
    static constexpr auto value = object(
        "parameter1", &T::parameter1,
        "parameter2", &T::parameter2
    );
};

// Add to variant
using msg_variant = std::variant<window_cmd, demodulation_cmd, 
                                userid_cmd, mute_cmd, chat_cmd, 
                                mycommand_cmd>;

// Update IDs array
static constexpr auto ids = std::array{
    "window", "demodulation", "userid", "mute", "chat", "mycommand"
};
```

### FFT Processing Pipeline

To modify FFT processing, edit [`src/fft_impl.cpp`](../src/fft_impl.cpp):

```cpp
void FFTW::process(const std::complex<float>* input, 
                   std::complex<float>* output,
                   int8_t* power_output) {
    // Custom pre-processing
    apply_window_function(input, windowed_input, fft_size);
    
    // Execute FFT
    fftwf_execute_dft(plan, 
                      reinterpret_cast<fftwf_complex*>(windowed_input),
                      reinterpret_cast<fftwf_complex*>(output));
    
    // Custom post-processing
    calculate_power_spectrum(output, power_output, fft_size);
    apply_averaging(power_output, averaged_output, fft_size);
}
```

### Adding Hardware Support

Create a new sample reader in `src/samplereader.cpp`:

```cpp
class SDRPlayReader : public SampleReader {
public:
    SDRPlayReader(const std::string& device) {
        // Initialize SDRPlay API
        sdrplay_api_Open();
        sdrplay_api_GetDevices(devices, &num_devices, MAX_DEVICES);
    }
    
    virtual size_t read(void* buffer, size_t size) override {
        // Read samples from SDRPlay
        return sdrplay_api_ReadSamples(buffer, size);
    }
    
    virtual ~SDRPlayReader() {
        sdrplay_api_Close();
    }
    
private:
    sdrplay_api_DeviceT devices[MAX_DEVICES];
    unsigned int num_devices;
};
```

---

## 5. Frontend Development

### Adding UI Components

Create a new Svelte component `frontend/src/lib/SignalMeter.svelte`:

```svelte
<script>
  export let signal = -100;
  export let peak = -100;
  
  $: smeter = calculateSMeter(signal);
  $: percentage = Math.max(0, Math.min(100, (signal + 130) * 2));
  
  function calculateSMeter(dbm) {
    if (dbm < -121) return "S0";
    if (dbm < -115) return "S1";
    if (dbm < -109) return "S2";
    if (dbm < -103) return "S3";
    if (dbm < -97) return "S4";
    if (dbm < -91) return "S5";
    if (dbm < -85) return "S6";
    if (dbm < -79) return "S7";
    if (dbm < -73) return "S8";
    if (dbm < -63) return "S9";
    return `S9+${Math.floor((dbm + 73) / 10) * 10}`;
  }
</script>

<div class="signal-meter">
  <div class="meter-display">
    <span class="smeter-value">{smeter}</span>
    <span class="dbm-value">{signal.toFixed(1)} dBm</span>
  </div>
  <div class="meter-bar-container">
    <div class="meter-bar" style="width: {percentage}%"></div>
    <div class="peak-marker" style="left: {(peak + 130) * 2}%"></div>
  </div>
</div>

<style>
  .signal-meter {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #444;
    border-radius: 4px;
    padding: 8px;
  }
  
  .meter-display {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
  }
  
  .smeter-value {
    font-weight: bold;
    color: #0f0;
  }
  
  .meter-bar-container {
    position: relative;
    height: 20px;
    background: #222;
    border-radius: 2px;
  }
  
  .meter-bar {
    height: 100%;
    background: linear-gradient(to right, 
      #0f0 0%, #0f0 60%, 
      #ff0 60%, #ff0 80%, 
      #f00 80%);
    transition: width 0.1s ease-out;
    border-radius: 2px;
  }
  
  .peak-marker {
    position: absolute;
    top: 0;
    width: 2px;
    height: 100%;
    background: #fff;
    transition: left 0.5s ease-out;
  }
</style>
```

### WebSocket Integration

Extend the audio client in `frontend/src/audio.js`:

```javascript
class ExtendedAudioClient extends SpectrumAudio {
  constructor(endpoint) {
    super(endpoint);
    this.customHandlers = new Map();
  }
  
  // Add custom message handler
  addMessageHandler(type, handler) {
    this.customHandlers.set(type, handler);
  }
  
  // Override socket message handler
  socketMessage(event) {
    if (event.data instanceof ArrayBuffer) {
      // Handle binary data
      super.socketMessage(event);
    } else {
      // Handle custom JSON messages
      try {
        const message = JSON.parse(event.data);
        if (this.customHandlers.has(message.type)) {
          this.customHandlers.get(message.type)(message);
        }
      } catch (e) {
        // Fall back to default handler
        super.socketMessage(event);
      }
    }
  }
  
  // Send custom command
  sendCustomCommand(command, data) {
    this.audioSocket.send(JSON.stringify({
      cmd: command,
      ...data
    }));
  }
}

// Usage
const audio = new ExtendedAudioClient('/audio');
audio.addMessageHandler('signal_info', (msg) => {
  console.log('Signal info:', msg.strength, msg.quality);
});
```

### Waterfall Enhancements

Add custom overlays to `frontend/src/waterfall.js`:

```javascript
class EnhancedWaterfall extends SpectrumWaterfall {
  constructor(endpoint) {
    super(endpoint);
    this.overlays = [];
  }
  
  addOverlay(overlay) {
    this.overlays.push(overlay);
  }
  
  drawWaterfall(arr, pxL, pxR, curL, curR) {
    // Draw base waterfall
    super.drawWaterfall(arr, pxL, pxR, curL, curR);
    
    // Draw overlays
    this.overlays.forEach(overlay => {
      overlay.draw(this.ctx, {
        left: curL,
        right: curR,
        pixelLeft: pxL,
        pixelRight: pxR,
        height: this.canvasHeight,
        freqToX: (freq) => this.freqToCanvasX(freq)
      });
    });
  }
}

// Custom overlay example
class GridOverlay {
  constructor(spacing = 100000) { // 100 kHz
    this.spacing = spacing;
  }
  
  draw(ctx, params) {
    const freqL = params.left;
    const freqR = params.right;
    
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    
    // Draw vertical grid lines
    let freq = Math.ceil(freqL / this.spacing) * this.spacing;
    while (freq <= freqR) {
      const x = params.freqToX(freq);
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, params.height);
      ctx.stroke();
      freq += this.spacing;
    }
    
    ctx.setLineDash([]);
  }
}
```

---

## 6. Testing and Debugging

### Unit Testing

Create test files in `tests/`:

```cpp
// tests/test_fft.cpp
#include <gtest/gtest.h>
#include "../src/fft.h"

TEST(FFTTest, PowerCalculation) {
    const int size = 1024;
    auto fft = std::make_unique<FFTW>(size, 1, 1, 0);
    
    // Create test signal
    std::vector<std::complex<float>> input(size);
    for (int i = 0; i < size; i++) {
        input[i] = std::complex<float>(
            sin(2 * M_PI * 100 * i / size), 0
        );
    }
    
    // Process
    std::vector<std::complex<float>> output(size);
    std::vector<int8_t> power(size);
    fft->process(input.data(), output.data(), power.data());
    
    // Verify peak at 100 Hz bin
    int peak_bin = 100;
    EXPECT_GT(power[peak_bin], power[peak_bin - 10]);
    EXPECT_GT(power[peak_bin], power[peak_bin + 10]);
}
```

### Performance Profiling

```bash
# CPU profiling with perf
sudo perf record -g ./build/spectrumserver --config config.toml
sudo perf report

# Memory profiling with valgrind
valgrind --tool=massif ./build/spectrumserver --config config.toml
ms_print massif.out.*

# GPU profiling (NVIDIA)
nsys profile ./build/spectrumserver --config config.toml
```

### Debug Logging

Add debug output using structured logging:

```cpp
// In src/utils.h
#define LOG_DEBUG(msg, ...) \
    if (debug_enabled) { \
        fprintf(stderr, "[DEBUG] %s:%d " msg "\n", \
                __FILE__, __LINE__, ##__VA_ARGS__); \
    }

// Usage
LOG_DEBUG("FFT processing: size=%d, time=%.3fms", 
          fft_size, processing_time);
```

### WebSocket Debugging

Use Chrome DevTools:

```javascript
// In browser console
const ws = new WebSocket('ws://localhost:9002/events');
ws.onmessage = (e) => console.log('Event:', JSON.parse(e.data));

// Monitor all WebSocket traffic
const origWebSocket = window.WebSocket;
window.WebSocket = function(...args) {
  console.log('WebSocket created:', args);
  const ws = new origWebSocket(...args);
  ws.addEventListener('message', e => {
    console.log('WS received:', e.data);
  });
  return ws;
};
```

---

## 7. Contributing Guidelines

### Code Style

#### C++ Style

```cpp
// Use .clang-format configuration
clang-format -i src/*.cpp src/*.h

// Style guidelines:
// - 4 space indentation
// - Opening braces on same line for functions
// - CamelCase for classes, snake_case for functions
// - Use modern C++ features (C++17/20/23)

class WaterfallProcessor {
public:
    WaterfallProcessor(int size) : buffer_size_{size} {
        buffer_ = std::make_unique<float[]>(buffer_size_);
    }
    
    void process_frame(std::span<const float> input) {
        std::transform(input.begin(), input.end(), 
                      buffer_.get(), 
                      [](float x) { return 20 * log10f(x); });
    }
    
private:
    int buffer_size_;
    std::unique_ptr<float[]> buffer_;
};
```

#### JavaScript Style

```javascript
// Use ESLint configuration
npm run lint

// Style guidelines:
// - 2 space indentation
// - Single quotes for strings
// - Semicolons required
// - Async/await over promises

class SignalProcessor {
  constructor(sampleRate) {
    this.sampleRate = sampleRate;
    this.filters = new Map();
  }
  
  async processSignal(data) {
    try {
      const filtered = await this.applyFilters(data);
      return this.demodulate(filtered);
    } catch (error) {
      console.error('Processing error:', error);
      return null;
    }
  }
  
  applyFilters(data) {
    return new Promise((resolve) => {
      // Apply filters asynchronously
      setTimeout(() => {
        const result = data.map(x => x * 0.5);
        resolve(result);
      }, 0);
    });
  }
}
```

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/my-new-feature

# Make changes and commit
git add -p  # Review changes
git commit -m "feat: add new demodulation mode

- Implement CW demodulation
- Add UI controls for CW offset
- Update documentation"

# Push and create PR
git push origin feature/my-new-feature
```

### Commit Message Format

Follow conventional commits:

- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation
- `style:` Code style
- `refactor:` Code refactoring
- `perf:` Performance improvement
- `test:` Tests
- `chore:` Maintenance

### Pull Request Process

1. **Fork and clone** the repository
2. **Create feature branch** from `main`
3. **Make changes** following code style
4. **Add tests** for new functionality
5. **Update documentation**
6. **Run tests** and ensure passing
7. **Submit PR** with clear description

### Testing Requirements

- Unit tests for new functions
- Integration tests for new endpoints
- Manual testing with real SDR hardware
- Performance benchmarks for critical paths

---

## Resources

- [C++ Core Guidelines](https://isocpp.github.io/CppCoreGuidelines/)
- [Svelte Documentation](https://svelte.dev/docs)
- [WebSocket Protocol](https://tools.ietf.org/html/rfc6455)
- [DSP Guide](https://www.dspguide.com/)

## Support

- GitHub Issues: Report bugs and request features
- Discussions: Ask questions and share ideas
- Wiki: Additional documentation and guides

---

**Happy coding!**