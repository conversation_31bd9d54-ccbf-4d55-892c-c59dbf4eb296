# PhantomSDR-Plus Waterfall Configuration
# Copy this file to .env.local and adjust the values as needed

# WebSocket Configuration
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:9002/waterfall
NEXT_PUBLIC_DEFAULT_FREQUENCY=93300000
NEXT_PUBLIC_DEFAULT_BANDWIDTH=30000000

# Performance Settings
NEXT_PUBLIC_ENABLE_GPU=true
NEXT_PUBLIC_BUFFER_SIZE=1000
NEXT_PUBLIC_REFRESH_RATE=60

# Debug Settings
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_LOG_LEVEL=info
