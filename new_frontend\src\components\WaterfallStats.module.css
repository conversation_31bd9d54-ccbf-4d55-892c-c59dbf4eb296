/**
 * Waterfall Statistics Styles
 * Clean, data-focused design with Apple aesthetics
 */

.container {
  position: absolute;
  top: 60px;
  left: 16px;
  width: 320px;
  background: rgba(28, 28, 30, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.content {
  padding: 16px 20px 20px;
  max-height: 500px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #8e8e93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Statistics Grid */
.statGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 11px;
  color: #8e8e93;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.value {
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* Configuration Grid */
.configGrid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.configItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  font-size: 13px;
}

.configLabel {
  color: #8e8e93;
  font-weight: 500;
}

.configValue {
  color: #ffffff;
  font-weight: 600;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* Performance Indicators */
.indicators {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.indicator {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.indicatorLabel {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
}

.progressBar {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease, background-color 0.3s ease;
  background: linear-gradient(90deg, currentColor 0%, currentColor 100%);
}

/* Scrollbar Styling */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: auto;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
  }
  
  .content {
    max-height: 250px;
  }
  
  .statGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .stat {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
  }
  
  .value {
    font-size: 14px;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  .container {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .header h3 {
    color: #000000;
  }
  
  .section h4 {
    color: #6d6d70;
  }
  
  .stat {
    background: rgba(0, 0, 0, 0.05);
  }
  
  .label {
    color: #6d6d70;
  }
  
  .value {
    color: #000000;
  }
  
  .configItem {
    background: rgba(0, 0, 0, 0.03);
  }
  
  .configLabel {
    color: #6d6d70;
  }
  
  .configValue {
    color: #000000;
  }
  
  .indicatorLabel {
    color: #6d6d70;
  }
  
  .progressBar {
    background: rgba(0, 0, 0, 0.1);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #ffffff;
  }
  
  .stat,
  .configItem {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  
  .progressBar {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .container {
    animation: none;
  }
  
  .statusDot {
    animation: none;
  }
  
  .progressFill {
    transition: none;
  }
}

/* Print Styles */
@media print {
  .container {
    position: static;
    width: 100%;
    box-shadow: none;
    border: 1px solid #000000;
    background: #ffffff;
    color: #000000;
  }
  
  .header h3,
  .value,
  .configValue {
    color: #000000;
  }
  
  .label,
  .configLabel,
  .indicatorLabel,
  .section h4 {
    color: #666666;
  }
}
