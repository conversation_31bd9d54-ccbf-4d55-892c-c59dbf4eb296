/**
 * Waterfall Color Mapping System
 * Provides Apple-inspired color schemes and high-performance color mapping
 */

import { ColorMap } from '@/types/waterfall';

/**
 * Predefined color maps inspired by Apple's design language
 */
export const COLOR_MAPS: Record<string, ColorMap> = {
  // Apple-inspired default colormap
  apple: {
    name: 'Apple',
    colors: [
      { position: 0.0, color: '#000000' },   // Black (no signal)
      { position: 0.1, color: '#1a1a2e' },   // Dark blue
      { position: 0.2, color: '#16213e' },   // Navy
      { position: 0.3, color: '#0f3460' },   // Deep blue
      { position: 0.4, color: '#533483' },   // Purple
      { position: 0.5, color: '#7209b7' },   // Magenta
      { position: 0.6, color: '#a663cc' },   // Light purple
      { position: 0.7, color: '#4cc9f0' },   // Cyan
      { position: 0.8, color: '#7bf1a8' },   // Green
      { position: 0.9, color: '#ffe66d' },   // Yellow
      { position: 1.0, color: '#ff6b6b' },   // Red (strong signal)
    ],
  },

  // Classic spectrum analyzer look
  spectrum: {
    name: 'Spectrum',
    colors: [
      { position: 0.0, color: '#000000' },
      { position: 0.2, color: '#000080' },
      { position: 0.4, color: '#0000ff' },
      { position: 0.6, color: '#00ff00' },
      { position: 0.8, color: '#ffff00' },
      { position: 1.0, color: '#ff0000' },
    ],
  },

  // Grayscale for accessibility
  grayscale: {
    name: 'Grayscale',
    colors: [
      { position: 0.0, color: '#000000' },
      { position: 1.0, color: '#ffffff' },
    ],
  },

  // High contrast for better visibility
  contrast: {
    name: 'High Contrast',
    colors: [
      { position: 0.0, color: '#000000' },
      { position: 0.25, color: '#ff0000' },
      { position: 0.5, color: '#ffff00' },
      { position: 0.75, color: '#00ff00' },
      { position: 1.0, color: '#ffffff' },
    ],
  },

  // Thermal imaging style
  thermal: {
    name: 'Thermal',
    colors: [
      { position: 0.0, color: '#000000' },
      { position: 0.2, color: '#800080' },
      { position: 0.4, color: '#ff0000' },
      { position: 0.6, color: '#ff8000' },
      { position: 0.8, color: '#ffff00' },
      { position: 1.0, color: '#ffffff' },
    ],
  },

  // Ocean depths theme
  ocean: {
    name: 'Ocean',
    colors: [
      { position: 0.0, color: '#000033' },
      { position: 0.3, color: '#000066' },
      { position: 0.5, color: '#0066cc' },
      { position: 0.7, color: '#00ccff' },
      { position: 0.9, color: '#66ffff' },
      { position: 1.0, color: '#ffffff' },
    ],
  },
};

/**
 * High-performance color mapper with GPU acceleration support
 */
export class ColorMapper {
  private colorLUT: Uint8ClampedArray;
  private currentColorMap: ColorMap;
  private brightness: number = 0;
  private contrast: number = 0;
  private readonly lutSize = 256;

  constructor(colorMap: ColorMap = COLOR_MAPS.apple) {
    this.currentColorMap = colorMap;
    this.colorLUT = new Uint8ClampedArray(this.lutSize * 4); // RGBA
    this.generateLUT();
  }

  /**
   * Generate color lookup table for fast mapping
   */
  private generateLUT(): void {
    const { colors } = this.currentColorMap;
    
    for (let i = 0; i < this.lutSize; i++) {
      const position = i / (this.lutSize - 1);
      const adjustedPosition = this.applyBrightnessContrast(position);
      const color = this.interpolateColor(colors, adjustedPosition);
      
      const baseIndex = i * 4;
      this.colorLUT[baseIndex] = color.r;     // Red
      this.colorLUT[baseIndex + 1] = color.g; // Green
      this.colorLUT[baseIndex + 2] = color.b; // Blue
      this.colorLUT[baseIndex + 3] = 255;     // Alpha
    }
  }

  /**
   * Apply brightness and contrast adjustments
   */
  private applyBrightnessContrast(position: number): number {
    // Apply brightness (-100 to 100)
    let adjusted = position + (this.brightness / 100);
    
    // Apply contrast (-100 to 100)
    const contrastFactor = (100 + this.contrast) / 100;
    adjusted = (adjusted - 0.5) * contrastFactor + 0.5;
    
    return Math.max(0, Math.min(1, adjusted));
  }

  /**
   * Interpolate color between color stops
   */
  private interpolateColor(colors: ColorMap['colors'], position: number): { r: number; g: number; b: number } {
    // Clamp position
    position = Math.max(0, Math.min(1, position));

    // Find surrounding color stops
    let lowerStop = colors[0];
    let upperStop = colors[colors.length - 1];

    for (let i = 0; i < colors.length - 1; i++) {
      if (position >= colors[i].position && position <= colors[i + 1].position) {
        lowerStop = colors[i];
        upperStop = colors[i + 1];
        break;
      }
    }

    // Calculate interpolation factor
    const range = upperStop.position - lowerStop.position;
    const factor = range === 0 ? 0 : (position - lowerStop.position) / range;

    // Parse colors
    const lowerColor = this.hexToRgb(lowerStop.color);
    const upperColor = this.hexToRgb(upperStop.color);

    // Interpolate
    return {
      r: Math.round(lowerColor.r + (upperColor.r - lowerColor.r) * factor),
      g: Math.round(lowerColor.g + (upperColor.g - lowerColor.g) * factor),
      b: Math.round(lowerColor.b + (upperColor.b - lowerColor.b) * factor),
    };
  }

  /**
   * Convert hex color to RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    } : { r: 0, g: 0, b: 0 };
  }

  /**
   * Map power values to colors
   * @param powerValues - Array of power values (-128 to 127)
   * @param output - Output RGBA array
   */
  mapColors(powerValues: Int8Array, output: Uint8ClampedArray): void {
    const length = powerValues.length;
    
    for (let i = 0; i < length; i++) {
      // Convert power value to LUT index
      // Power values are typically in range -128 to 127
      const normalizedValue = (powerValues[i] + 128) / 255;
      const lutIndex = Math.round(normalizedValue * (this.lutSize - 1));
      const clampedIndex = Math.max(0, Math.min(this.lutSize - 1, lutIndex));
      
      // Copy color from LUT
      const lutOffset = clampedIndex * 4;
      const outputOffset = i * 4;
      
      output[outputOffset] = this.colorLUT[lutOffset];         // Red
      output[outputOffset + 1] = this.colorLUT[lutOffset + 1]; // Green
      output[outputOffset + 2] = this.colorLUT[lutOffset + 2]; // Blue
      output[outputOffset + 3] = this.colorLUT[lutOffset + 3]; // Alpha
    }
  }

  /**
   * Set color map
   */
  setColorMap(colorMap: ColorMap): void {
    this.currentColorMap = colorMap;
    this.generateLUT();
  }

  /**
   * Set brightness adjustment
   * @param brightness - Brightness value (-100 to 100)
   */
  setBrightness(brightness: number): void {
    this.brightness = Math.max(-100, Math.min(100, brightness));
    this.generateLUT();
  }

  /**
   * Set contrast adjustment
   * @param contrast - Contrast value (-100 to 100)
   */
  setContrast(contrast: number): void {
    this.contrast = Math.max(-100, Math.min(100, contrast));
    this.generateLUT();
  }

  /**
   * Get current color map
   */
  getColorMap(): ColorMap {
    return this.currentColorMap;
  }

  /**
   * Get current brightness
   */
  getBrightness(): number {
    return this.brightness;
  }

  /**
   * Get current contrast
   */
  getContrast(): number {
    return this.contrast;
  }

  /**
   * Create a color map preview
   * @param width - Preview width in pixels
   * @param height - Preview height in pixels
   * @returns ImageData for preview
   */
  createPreview(width: number, height: number): ImageData {
    const imageData = new ImageData(width, height);
    const data = imageData.data;

    for (let x = 0; x < width; x++) {
      const position = x / (width - 1);
      const color = this.interpolateColor(this.currentColorMap.colors, position);
      
      for (let y = 0; y < height; y++) {
        const index = (y * width + x) * 4;
        data[index] = color.r;     // Red
        data[index + 1] = color.g; // Green
        data[index + 2] = color.b; // Blue
        data[index + 3] = 255;     // Alpha
      }
    }

    return imageData;
  }
}
