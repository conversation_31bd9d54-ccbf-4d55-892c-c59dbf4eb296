/**
 * Waterfall Controls Component
 * Provides user interface controls for waterfall display parameters
 */

'use client';

import React, { useState, useCallback } from 'react';
import { WaterfallConfig, WaterfallDisplayParams, WaterfallRenderOptions } from '@/types/waterfall';
import { COLOR_MAPS } from '@/lib/rendering/colormap';
import styles from './WaterfallControls.module.css';

interface WaterfallControlsProps {
  config: WaterfallConfig | null;
  onBrightnessChange: (brightness: number) => void;
  onContrastChange: (contrast: number) => void;
  onColorMapChange: (colorMapName: string) => void;
  onDisplayParamsChange: (params: Partial<WaterfallDisplayParams>) => void;
  onRenderOptionsChange: (options: Partial<WaterfallRenderOptions>) => void;
  onClear: () => void;
}

export const WaterfallControls: React.FC<WaterfallControlsProps> = ({
  config,
  onBrightnessChange,
  onContrastChange,
  onColorMapChange,
  onDisplayParamsChange,
  onRenderOptionsChange,
  onClear,
}) => {
  const [brightness, setBrightness] = useState(0);
  const [contrast, setContrast] = useState(0);
  const [selectedColorMap, setSelectedColorMap] = useState('apple');
  const [smoothing, setSmoothing] = useState(true);
  const [refreshRate, setRefreshRate] = useState(60);

  const handleBrightnessChange = useCallback((value: number) => {
    setBrightness(value);
    onBrightnessChange(value);
  }, [onBrightnessChange]);

  const handleContrastChange = useCallback((value: number) => {
    setContrast(value);
    onContrastChange(value);
  }, [onContrastChange]);

  const handleColorMapChange = useCallback((colorMapName: string) => {
    setSelectedColorMap(colorMapName);
    onColorMapChange(colorMapName);
  }, [onColorMapChange]);

  const handleSmoothingChange = useCallback((enabled: boolean) => {
    setSmoothing(enabled);
    onRenderOptionsChange({ smoothing: enabled });
  }, [onRenderOptionsChange]);

  const handleRefreshRateChange = useCallback((rate: number) => {
    setRefreshRate(rate);
    onRenderOptionsChange({ refreshRate: rate });
  }, [onRenderOptionsChange]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3>Display Controls</h3>
      </div>

      <div className={styles.content}>
        {/* Color and Appearance */}
        <div className={styles.section}>
          <h4>Color & Appearance</h4>
          
          <div className={styles.control}>
            <label>Color Map</label>
            <select
              value={selectedColorMap}
              onChange={(e) => handleColorMapChange(e.target.value)}
              className={styles.select}
            >
              {Object.entries(COLOR_MAPS).map(([key, colorMap]) => (
                <option key={key} value={key}>
                  {colorMap.name}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.control}>
            <label>Brightness</label>
            <input
              type="range"
              min="-100"
              max="100"
              value={brightness}
              onChange={(e) => handleBrightnessChange(Number(e.target.value))}
              className={styles.slider}
            />
            <span className={styles.value}>{brightness}</span>
          </div>

          <div className={styles.control}>
            <label>Contrast</label>
            <input
              type="range"
              min="-100"
              max="100"
              value={contrast}
              onChange={(e) => handleContrastChange(Number(e.target.value))}
              className={styles.slider}
            />
            <span className={styles.value}>{contrast}</span>
          </div>
        </div>

        {/* Rendering Options */}
        <div className={styles.section}>
          <h4>Rendering</h4>
          
          <div className={styles.control}>
            <label>
              <input
                type="checkbox"
                checked={smoothing}
                onChange={(e) => handleSmoothingChange(e.target.checked)}
                className={styles.checkbox}
              />
              Enable Smoothing
            </label>
          </div>

          <div className={styles.control}>
            <label>Refresh Rate (FPS)</label>
            <select
              value={refreshRate}
              onChange={(e) => handleRefreshRateChange(Number(e.target.value))}
              className={styles.select}
            >
              <option value={30}>30 FPS</option>
              <option value={60}>60 FPS</option>
              <option value={120}>120 FPS</option>
            </select>
          </div>
        </div>

        {/* Configuration Info */}
        {config && (
          <div className={styles.section}>
            <h4>Configuration</h4>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <span>Sample Rate:</span>
                <span>{(config.sps / 1e6).toFixed(1)} MHz</span>
              </div>
              <div className={styles.infoItem}>
                <span>FFT Size:</span>
                <span>{config.fft_size.toLocaleString()}</span>
              </div>
              <div className={styles.infoItem}>
                <span>Compression:</span>
                <span>{config.waterfall_compression.toUpperCase()}</span>
              </div>
              <div className={styles.infoItem}>
                <span>Base Frequency:</span>
                <span>{(config.basefreq / 1e6).toFixed(3)} MHz</span>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className={styles.section}>
          <h4>Actions</h4>
          <button onClick={onClear} className={styles.actionButton}>
            Clear Display
          </button>
        </div>
      </div>
    </div>
  );
};
