/**
 * PhantomSDR-Plus Waterfall Demo Page
 * Demonstrates the complete waterfall implementation
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Waterfall } from '@/components/Waterfall';
import { WaterfallEventHandlers } from '@/types/waterfall';
import styles from './page.module.css';

export default function WaterfallDemo() {
  const [websocketUrl, setWebsocketUrl] = useState('ws://localhost:9002/waterfall');
  const [currentFrequency, setCurrentFrequency] = useState<number | null>(null);
  const [connectionLog, setConnectionLog] = useState<string[]>([]);

  // Event handlers for waterfall
  const eventHandlers: WaterfallEventHandlers = {
    onConnect: useCallback((config) => {
      console.log('Waterfall connected:', config);
      setConnectionLog(prev => [...prev, `Connected to ${config.basefreq / 1e6} MHz SDR`]);
    }, []),

    onDisconnect: useCallback(() => {
      console.log('Waterfall disconnected');
      setConnectionLog(prev => [...prev, 'Disconnected from SDR']);
    }, []),

    onFrequencyChange: useCallback((frequency) => {
      setCurrentFrequency(frequency);
      console.log('Frequency changed:', frequency);
    }, []),

    onError: useCallback((error) => {
      console.error('Waterfall error:', error);
      setConnectionLog(prev => [...prev, `Error: ${error.message}`]);
    }, []),

    onDataReceived: useCallback((data) => {
      // Optional: Log data reception for debugging
      // console.log('Data received:', data.frameNum);
    }, []),
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setWebsocketUrl(event.target.value);
  };

  const clearLog = () => {
    setConnectionLog([]);
  };

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <h1>PhantomSDR-Plus Waterfall</h1>
        <p>Real-time spectrum waterfall display with React and Next.js</p>
      </header>

      <main className={styles.main}>
        {/* Connection Settings */}
        <section className={styles.settings}>
          <h2>Connection Settings</h2>
          <div className={styles.settingsGrid}>
            <div className={styles.inputGroup}>
              <label htmlFor="websocket-url">WebSocket URL:</label>
              <input
                id="websocket-url"
                type="text"
                value={websocketUrl}
                onChange={handleUrlChange}
                className={styles.input}
                placeholder="ws://localhost:9002/waterfall"
              />
            </div>
            {currentFrequency && (
              <div className={styles.frequencyDisplay}>
                <span>Current Frequency:</span>
                <span className={styles.frequency}>
                  {(currentFrequency / 1e6).toFixed(3)} MHz
                </span>
              </div>
            )}
          </div>
        </section>

        {/* Waterfall Display */}
        <section className={styles.waterfallSection}>
          <Waterfall
            websocketUrl={websocketUrl}
            width={1200}
            height={600}
            displayParams={{
              frequencyRange: { start: 0, end: 30000000 },
              timeRange: 60,
              colormap: 'apple',
              brightness: 0,
              contrast: 0,
              zoom: { level: 0, center: 15000000 },
            }}
            renderOptions={{
              smoothing: true,
              interpolation: 'linear',
              refreshRate: 60,
              bufferSize: 1000,
              enableGPU: true,
            }}
            eventHandlers={eventHandlers}
            className={styles.waterfall}
          />
        </section>

        {/* Connection Log */}
        <section className={styles.logSection}>
          <div className={styles.logHeader}>
            <h3>Connection Log</h3>
            <button onClick={clearLog} className={styles.clearButton}>
              Clear
            </button>
          </div>
          <div className={styles.log}>
            {connectionLog.length === 0 ? (
              <p className={styles.emptyLog}>No log entries yet...</p>
            ) : (
              connectionLog.map((entry, index) => (
                <div key={index} className={styles.logEntry}>
                  <span className={styles.timestamp}>
                    {new Date().toLocaleTimeString()}
                  </span>
                  <span className={styles.message}>{entry}</span>
                </div>
              ))
            )}
          </div>
        </section>

        {/* Feature Overview */}
        <section className={styles.features}>
          <h2>Features</h2>
          <div className={styles.featureGrid}>
            <div className={styles.feature}>
              <h3>🚀 High Performance</h3>
              <p>60fps rendering with WebAssembly acceleration and GPU optimization</p>
            </div>
            <div className={styles.feature}>
              <h3>🎨 Apple Design</h3>
              <p>Beautiful, responsive UI following Apple's Human Interface Guidelines</p>
            </div>
            <div className={styles.feature}>
              <h3>📊 Real-time Data</h3>
              <p>Live spectrum data with ZSTD and AV1 decompression support</p>
            </div>
            <div className={styles.feature}>
              <h3>⚙️ Full Control</h3>
              <p>Complete frequency control, zoom, and display customization</p>
            </div>
            <div className={styles.feature}>
              <h3>📱 Responsive</h3>
              <p>Works perfectly on desktop, tablet, and mobile devices</p>
            </div>
            <div className={styles.feature}>
              <h3>🔧 Production Ready</h3>
              <p>TypeScript, error handling, and comprehensive testing</p>
            </div>
          </div>
        </section>

        {/* Usage Instructions */}
        <section className={styles.instructions}>
          <h2>Usage Instructions</h2>
          <div className={styles.instructionsList}>
            <div className={styles.instruction}>
              <strong>Connect:</strong> Enter your PhantomSDR-Plus WebSocket URL above
            </div>
            <div className={styles.instruction}>
              <strong>Navigate:</strong> Click and drag on the waterfall to select frequencies
            </div>
            <div className={styles.instruction}>
              <strong>Controls:</strong> Press 'C' to toggle display controls
            </div>
            <div className={styles.instruction}>
              <strong>Statistics:</strong> Press 'S' to view performance statistics
            </div>
            <div className={styles.instruction}>
              <strong>Fullscreen:</strong> Press 'F' to toggle fullscreen mode
            </div>
            <div className={styles.instruction}>
              <strong>Clear:</strong> Press 'R' to clear the waterfall display
            </div>
          </div>
        </section>
      </main>

      <footer className={styles.footer}>
        <p>
          PhantomSDR-Plus Waterfall Component • Built with React, Next.js, and TypeScript
        </p>
        <p>
          <a href="https://github.com/Steven9101/PhantomSDR-Plus" target="_blank" rel="noopener noreferrer">
            View on GitHub
          </a>
        </p>
      </footer>
    </div>
  );
}
