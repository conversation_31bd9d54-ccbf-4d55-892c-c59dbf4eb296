# PhantomSDR-Plus API Reference

This document provides detailed information about the PhantomSDR-Plus WebSocket API and protocols.

## Table of Contents

1. [WebSocket Endpoints](#1-websocket-endpoints)
2. [Message Protocol](#2-message-protocol)
3. [Binary Data Formats](#3-binary-data-formats)
4. [Client Commands](#4-client-commands)
5. [Server Responses](#5-server-responses)
6. [<PERSON><PERSON><PERSON>ling](#6-error-handling)
7. [Code Examples](#7-code-examples)

---

## 1. WebSocket Endpoints

PhantomSDR-Plus exposes the following WebSocket endpoints:

### `/audio`
- **Purpose**: Audio streaming with demodulation
- **Protocol**: Text (JSON) + Binary (CBOR)
- **Compression**: FLAC or Opus

### `/waterfall`
- **Purpose**: Waterfall spectrum data
- **Protocol**: Text (JSON) + Binary
- **Compression**: ZSTD

### `/events`
- **Purpose**: Server statistics and user information
- **Protocol**: Text (JSON) only
- **Updates**: Every second

### `/chat`
- **Purpose**: Real-time chat messaging
- **Protocol**: Text only
- **Features**: Message history, profanity filter

---

## 2. Message Protocol

### Initial Connection

All endpoints (except chat) send an initial JSON configuration message:

```json
{
  "sps": 64000000,              // Sample rate
  "audio_max_sps": 12000,       // Max audio sample rate
  "audio_max_fft": 512,         // Max audio FFT size
  "fft_size": 131072,           // Main FFT size
  "fft_result_size": 65536,     // FFT result size (half for real signals)
  "waterfall_size": 1024,       // Min waterfall size
  "basefreq": 0,                // Base frequency (Hz)
  "total_bandwidth": 32000000,  // Total bandwidth (Hz)
  "defaults": {
    "frequency": 14074000,      // Default tune frequency
    "modulation": "USB",        // Default mode
    "l": 456,                   // Left edge (FFT bins)
    "m": 457.5,                 // Center (FFT bins)
    "r": 460                    // Right edge (FFT bins)
  },
  "waterfall_compression": "zstd",
  "audio_compression": "flac",
  "grid_locator": "JO21",      // Maidenhead locator
  "smeter_offset": 0,           // S-meter calibration
  "markers": "[{\"frequency\":14074000,\"name\":\"FT8\",\"mode\":\"USB\"}]"
}
```

### Message Flow

1. Client connects to endpoint
2. Server sends initial configuration
3. Client sends commands (JSON)
4. Server streams data (Binary/JSON)

---

## 3. Binary Data Formats

### Waterfall Data Packet

```
Header (12 bytes):
┌─────────────┬─────────────┬─────────────┐
│ Frame Num   │ Left Index  │ Right Index │
│ (uint32)    │ (int32)     │ (int32)     │
└─────────────┴─────────────┴─────────────┘

Data:
┌─────────────────────────────────────────┐
│ ZSTD Compressed Power Values (int8[])   │
└─────────────────────────────────────────┘
```

### Audio Data Packet

CBOR encoded structure:

```javascript
{
  pwr: -73.5,              // Signal power (dBFS)
  data: Uint8Array(...)    // Compressed audio (FLAC/Opus)
}
```

### Power Value Encoding

- **Range**: -128 to +127 dB
- **Type**: int8_t
- **Reference**: 0 dB = full scale

---

## 4. Client Commands

### Window Configuration

```json
{
  "cmd": "window",
  "l": 456,        // Left edge (FFT bin index)
  "m": 457.5,      // Center frequency (FFT bin index) - optional
  "r": 460,        // Right edge (FFT bin index)
  "level": 0       // Zoom level (0-4) - optional
}
```

### Demodulation Mode

```json
{
  "cmd": "demodulation",
  "demodulation": "USB"  // Options: USB, LSB, AM, FM
}
```

### User Identification

```json
{
  "cmd": "userid",
  "userid": "unique_id_here"  // Max 32 characters
}
```

### Mute Control

```json
{
  "cmd": "mute",
  "mute": true   // true = muted, false = unmuted
}
```

### Chat Message

```json
{
  "cmd": "chat",
  "username": "User123",     // Max 14 characters
  "message": "Hello world"   // Max 200 characters
}
```

---

## 5. Server Responses

### Events Endpoint Updates

```json
{
  "waterfall_clients": 35,        // Number of waterfall connections
  "signal_clients": 18,           // Number of audio connections
  "signal_changes": {             // User frequency changes
    "user_id_1": [456, 457.5, 460],
    "user_id_2": [500, 501.5, 503]
  },
  "waterfall_kbits": 8960.5,      // Waterfall bandwidth (kbit/s)
  "audio_kbits": 2304.2           // Audio bandwidth (kbit/s)
}
```

### Chat Messages

```
2025-06-13 15:30:45 User123: Hello everyone!
Chat history:
2025-06-13 15:29:12 User456: Welcome to the WebSDR
2025-06-13 15:28:03 User789: Testing 1-2-3
```

---

## 6. Error Handling

### Connection Errors

- Invalid endpoint: Connection closed immediately
- Rate limiting: Connection closed with status 1008
- Server overload: Connection closed with status 1001

### Message Errors

Invalid JSON commands are silently ignored. The server validates:
- Command structure
- Parameter ranges
- Data types

### Binary Data Errors

Corrupted binary data results in:
- Dropped frames (waterfall)
- Audio silence (audio)

---

## 7. Code Examples

### JavaScript WebSocket Client

```javascript
class PhantomSDRClient {
  constructor(host = 'localhost', port = 9002) {
    this.baseUrl = `ws://${host}:${port}`;
    this.waterfall = null;
    this.audio = null;
    this.events = null;
  }

  async connectWaterfall() {
    return new Promise((resolve, reject) => {
      this.waterfall = new WebSocket(`${this.baseUrl}/waterfall`);
      this.waterfall.binaryType = 'arraybuffer';
      
      this.waterfall.onopen = () => {
        console.log('Waterfall connected');
      };
      
      this.waterfall.onmessage = (event) => {
        if (typeof event.data === 'string') {
          // Initial configuration
          const config = JSON.parse(event.data);
          this.config = config;
          resolve(config);
        } else {
          // Binary waterfall data
          this.processWaterfall(event.data);
        }
      };
      
      this.waterfall.onerror = reject;
    });
  }

  processWaterfall(arrayBuffer) {
    const view = new DataView(arrayBuffer);
    const frameNum = view.getUint32(0, true);
    const left = view.getInt32(4, true);
    const right = view.getInt32(8, true);
    
    // Compressed data starts at offset 12
    const compressedData = new Uint8Array(arrayBuffer, 12);
    
    // Decompress using ZSTD library
    // const data = ZSTD.decompress(compressedData);
    
    console.log(`Frame ${frameNum}: bins ${left}-${right}`);
  }

  setWindow(left, center, right) {
    this.waterfall.send(JSON.stringify({
      cmd: 'window',
      l: left,
      m: center,
      r: right
    }));
  }

  setUserID(id) {
    this.waterfall.send(JSON.stringify({
      cmd: 'userid',
      userid: id
    }));
  }
}

// Usage
const client = new PhantomSDRClient();
await client.connectWaterfall();
client.setWindow(0, 512, 1024);
client.setUserID('my_app_123');
```

### Python Client

```python
import asyncio
import websockets
import json
import struct
import zstandard as zstd

class PhantomSDRClient:
    def __init__(self, host='localhost', port=9002):
        self.base_url = f'ws://{host}:{port}'
        self.config = None
        self.dctx = zstd.ZstdDecompressor()
        
    async def connect_waterfall(self):
        self.waterfall = await websockets.connect(f'{self.base_url}/waterfall')
        
        # Get initial configuration
        config_msg = await self.waterfall.recv()
        self.config = json.loads(config_msg)
        print(f"Connected to {self.config.get('name', 'WebSDR')}")
        
        return self.config
        
    async def process_waterfall_stream(self):
        async for message in self.waterfall:
            if isinstance(message, bytes):
                # Parse header
                frame_num = struct.unpack('<I', message[0:4])[0]
                left = struct.unpack('<i', message[4:8])[0]
                right = struct.unpack('<i', message[8:12])[0]
                
                # Decompress data
                compressed = message[12:]
                data = self.dctx.decompress(compressed)
                power_values = struct.unpack(f'{len(data)}b', data)
                
                print(f"Frame {frame_num}: {len(power_values)} bins")
                # Process power values...
                
    async def set_window(self, left, center, right):
        await self.waterfall.send(json.dumps({
            'cmd': 'window',
            'l': left,
            'm': center,
            'r': right
        }))
        
    async def set_userid(self, userid):
        await self.waterfall.send(json.dumps({
            'cmd': 'userid',
            'userid': userid
        }))

# Usage
async def main():
    client = PhantomSDRClient()
    await client.connect_waterfall()
    await client.set_window(0, 512, 1024)
    await client.set_userid('python_client_123')
    await client.process_waterfall_stream()

asyncio.run(main())
```

### Audio Stream Processing

```javascript
import { decode } from 'cbor-x';

class AudioProcessor {
  constructor(websocketUrl) {
    this.ws = new WebSocket(websocketUrl);
    this.ws.binaryType = 'arraybuffer';
    this.audioContext = new AudioContext();
    this.sampleRate = 12000;
  }

  async connect() {
    return new Promise((resolve) => {
      this.ws.onmessage = (event) => {
        if (typeof event.data === 'string') {
          const config = JSON.parse(event.data);
          this.sampleRate = config.audio_max_sps;
          resolve(config);
        } else {
          this.processAudio(event.data);
        }
      };
    });
  }

  processAudio(arrayBuffer) {
    // Decode CBOR
    const packet = decode(new Uint8Array(arrayBuffer));
    const power = packet.pwr;
    const compressedAudio = packet.data;
    
    console.log(`Audio power: ${power.toFixed(1)} dB`);
    
    // Decode FLAC/Opus audio
    // ... decoder implementation ...
    
    // Play audio
    this.playPCM(decodedSamples);
  }

  playPCM(samples) {
    const buffer = this.audioContext.createBuffer(
      1, samples.length, this.sampleRate
    );
    buffer.copyToChannel(samples, 0);
    
    const source = this.audioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(this.audioContext.destination);
    source.start();
  }

  setDemodulation(mode) {
    this.ws.send(JSON.stringify({
      cmd: 'demodulation',
      demodulation: mode
    }));
  }

  setFrequency(left, center, right) {
    this.ws.send(JSON.stringify({
      cmd: 'window',
      l: left,
      m: center,
      r: right
    }));
  }
}
```

### Events Monitor

```javascript
class EventsMonitor {
  constructor(websocketUrl) {
    this.ws = new WebSocket(websocketUrl);
    this.users = {};
  }

  connect() {
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.signal_changes) {
        Object.entries(data.signal_changes).forEach(([user, freq]) => {
          if (freq[0] === -1 && freq[1] === -1) {
            console.log(`User ${user} disconnected`);
            delete this.users[user];
          } else {
            console.log(`User ${user} tuned to ${freq[1]}`);
            this.users[user] = freq;
          }
        });
      }
      
      console.log(`Connected users: ${data.signal_clients}`);
      console.log(`Bandwidth: ${data.waterfall_kbits + data.audio_kbits} kbit/s`);
    };
  }
}
```

---

## Additional Resources

- [Frontend Integration Guide](FRONTEND_GUIDE.md)
- [Protocol Specifications](PROTOCOL_SPEC.md)
- [Performance Tuning](PERFORMANCE.md)

## Version

This API documentation is for PhantomSDR-Plus version 1.0.0

---