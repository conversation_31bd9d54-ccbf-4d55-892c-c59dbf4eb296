/**
 * PhantomSDR-Plus Waterfall WebSocket Client
 * Handles real-time communication with the PhantomSDR-Plus backend
 * Maintains full compatibility with the existing protocol
 */

import { decode } from 'cbor-x';
import { 
  WaterfallConfig, 
  WaterfallData, 
  WaterfallCommand, 
  WaterfallStats,
  WaterfallError,
  WaterfallErrorType,
  WebSocketState
} from '@/types/waterfall';
import { createZstdDecoder, ZstdDecoder, ZstdDecoderFallback } from '@/lib/compression/zstd-decoder';
import { createAV1Decoder, AV1Decoder, AV1DecoderFallback } from '@/lib/compression/av1-decoder';

/**
 * Event handlers for waterfall client
 */
export interface WaterfallClientEventHandlers {
  onConnect?: (config: WaterfallConfig) => void;
  onDisconnect?: () => void;
  onData?: (data: WaterfallData) => void;
  onError?: (error: WaterfallError) => void;
  onStatsUpdate?: (stats: WaterfallStats) => void;
}

/**
 * High-performance WebSocket client for PhantomSDR-Plus waterfall data
 * Implements the complete protocol with compression support
 */
export class WaterfallClient {
  private ws: WebSocket | null = null;
  private config: WaterfallConfig | null = null;
  private zstdDecoder: ZstdDecoder | ZstdDecoderFallback | null = null;
  private av1Decoder: AV1Decoder | AV1DecoderFallback | null = null;
  private eventHandlers: WaterfallClientEventHandlers = {};
  private stats: WaterfallStats;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private statsInterval: NodeJS.Timeout | null = null;
  private lastDataTime = 0;

  constructor(
    private readonly websocketUrl: string,
    eventHandlers: WaterfallClientEventHandlers = {}
  ) {
    this.eventHandlers = eventHandlers;
    this.stats = this.initializeStats();
    this.initializeDecoders();
  }

  /**
   * Initialize performance statistics
   */
  private initializeStats(): WaterfallStats {
    return {
      framesReceived: 0,
      framesDropped: 0,
      dataRate: 0,
      compressionRatio: 0,
      averageLatency: 0,
      connectionStatus: 'disconnected',
    };
  }

  /**
   * Initialize compression decoders
   */
  private async initializeDecoders(): Promise<void> {
    try {
      this.zstdDecoder = await createZstdDecoder();
      this.av1Decoder = await createAV1Decoder();
    } catch (error) {
      console.error('Failed to initialize decoders:', error);
    }
  }

  /**
   * Connect to the PhantomSDR-Plus waterfall WebSocket
   */
  async connect(): Promise<WaterfallConfig> {
    return new Promise((resolve, reject) => {
      try {
        this.stats.connectionStatus = 'connecting';
        this.ws = new WebSocket(this.websocketUrl);
        this.ws.binaryType = 'arraybuffer';

        // Connection opened
        this.ws.onopen = () => {
          console.log('Waterfall WebSocket connected');
          this.stats.connectionStatus = 'connected';
          this.reconnectAttempts = 0;
          this.startStatsMonitoring();
        };

        // Message received
        this.ws.onmessage = (event) => {
          if (typeof event.data === 'string') {
            // Initial configuration message
            try {
              this.config = JSON.parse(event.data) as WaterfallConfig;
              this.eventHandlers.onConnect?.(this.config);
              resolve(this.config);
            } catch (error) {
              const waterfallError = new WaterfallError(
                WaterfallErrorType.INVALID_DATA,
                'Invalid configuration data',
                error
              );
              this.eventHandlers.onError?.(waterfallError);
              reject(waterfallError);
            }
          } else {
            // Binary waterfall data
            this.handleBinaryData(event.data as ArrayBuffer);
          }
        };

        // Connection closed
        this.ws.onclose = (event) => {
          console.log('Waterfall WebSocket closed:', event.code, event.reason);
          this.stats.connectionStatus = 'disconnected';
          this.stopStatsMonitoring();
          this.eventHandlers.onDisconnect?.();
          
          // Attempt reconnection if not intentional
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        // Connection error
        this.ws.onerror = (event) => {
          console.error('Waterfall WebSocket error:', event);
          this.stats.connectionStatus = 'error';
          const error = new WaterfallError(
            WaterfallErrorType.WEBSOCKET_ERROR,
            'WebSocket connection error'
          );
          this.eventHandlers.onError?.(error);
          reject(error);
        };

      } catch (error) {
        const waterfallError = new WaterfallError(
          WaterfallErrorType.CONNECTION_FAILED,
          'Failed to create WebSocket connection',
          error
        );
        this.eventHandlers.onError?.(waterfallError);
        reject(waterfallError);
      }
    });
  }

  /**
   * Handle binary waterfall data
   */
  private async handleBinaryData(arrayBuffer: ArrayBuffer): Promise<void> {
    try {
      const startTime = performance.now();
      this.lastDataTime = Date.now();

      // Decode CBOR packet
      const packet = decode(new Uint8Array(arrayBuffer));
      
      // Extract packet data
      const frameNum = packet.frame_num;
      const left = packet.l;
      const right = packet.r;
      const compressedData = new Uint8Array(packet.data);

      // Decompress based on configuration
      let powerValues: Int8Array;
      
      if (this.config?.waterfall_compression === 'zstd') {
        if (!this.zstdDecoder) {
          throw new Error('ZSTD decoder not initialized');
        }
        powerValues = await this.zstdDecoder.decompress(compressedData);
      } else if (this.config?.waterfall_compression === 'av1') {
        if (!this.av1Decoder) {
          throw new Error('AV1 decoder not initialized');
        }
        const av1Data = await this.av1Decoder.decode(compressedData);
        // For AV1, we get multiple lines - use the first one for now
        powerValues = av1Data[0]?.powerValues || new Int8Array(0);
      } else {
        throw new Error(`Unsupported compression: ${this.config?.waterfall_compression}`);
      }

      // Create waterfall data object
      const waterfallData: WaterfallData = {
        frameNum,
        left,
        right,
        powerValues,
        timestamp: Date.now(),
      };

      // Update statistics
      this.updateStats(arrayBuffer.byteLength, powerValues.length, performance.now() - startTime);

      // Emit data event
      this.eventHandlers.onData?.(waterfallData);

    } catch (error) {
      console.error('Failed to process waterfall data:', error);
      this.stats.framesDropped++;
      
      const waterfallError = new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'Failed to process waterfall data',
        error
      );
      this.eventHandlers.onError?.(waterfallError);
    }
  }

  /**
   * Update performance statistics
   */
  private updateStats(compressedSize: number, decompressedSize: number, processingTime: number): void {
    this.stats.framesReceived++;
    this.stats.compressionRatio = decompressedSize / compressedSize;
    this.stats.averageLatency = (this.stats.averageLatency + processingTime) / 2;
  }

  /**
   * Send command to backend
   */
  sendCommand(command: WaterfallCommand): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new WaterfallError(
        WaterfallErrorType.WEBSOCKET_ERROR,
        'WebSocket not connected'
      );
    }

    try {
      this.ws.send(JSON.stringify(command));
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.WEBSOCKET_ERROR,
        'Failed to send command',
        error
      );
    }
  }

  /**
   * Set frequency window
   */
  setWindow(left: number, right: number, center?: number, level?: number): void {
    this.sendCommand({
      cmd: 'window',
      l: left,
      r: right,
      m: center,
      level,
    });
  }

  /**
   * Set user ID for correlation
   */
  setUserId(userId: string): void {
    this.sendCommand({
      cmd: 'userid',
      userid: userId.substring(0, 32), // Backend limits to 32 characters
    });
  }

  /**
   * Set mute state
   */
  setMute(mute: boolean): void {
    this.sendCommand({
      cmd: 'mute',
      mute,
    });
  }

  /**
   * Get current statistics
   */
  getStats(): WaterfallStats {
    return { ...this.stats };
  }

  /**
   * Get WebSocket state
   */
  getWebSocketState(): WebSocketState | null {
    if (!this.ws) return null;

    return {
      readyState: this.ws.readyState,
      url: this.ws.url,
      protocol: this.ws.protocol,
      extensions: this.ws.extensions,
      bufferedAmount: this.ws.bufferedAmount,
    };
  }

  /**
   * Start monitoring statistics
   */
  private startStatsMonitoring(): void {
    this.statsInterval = setInterval(() => {
      // Calculate data rate
      const now = Date.now();
      const timeSinceLastData = now - this.lastDataTime;
      
      if (timeSinceLastData > 5000) { // 5 seconds without data
        this.stats.dataRate = 0;
      }

      this.eventHandlers.onStatsUpdate?.(this.stats);
    }, 1000);
  }

  /**
   * Stop monitoring statistics
   */
  private stopStatsMonitoring(): void {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
      this.statsInterval = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000); // Exponential backoff, max 30s

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.stopStatsMonitoring();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.stats.connectionStatus = 'disconnected';
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.disconnect();
    
    this.zstdDecoder?.destroy();
    this.av1Decoder?.destroy();
    
    this.zstdDecoder = null;
    this.av1Decoder = null;
  }
}
