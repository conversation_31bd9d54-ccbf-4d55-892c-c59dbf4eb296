@echo off
echo Installing PhantomSDR-Plus Waterfall Frontend...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 18 or later from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not available
    pause
    exit /b 1
)

echo npm version:
npm --version
echo.

REM Clean any previous installations
if exist node_modules (
    echo Cleaning previous installation...
    rmdir /s /q node_modules
)

if exist package-lock.json (
    del package-lock.json
)

echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo.
    echo Installation failed. This might be due to:
    echo 1. Network connectivity issues
    echo 2. Missing build tools
    echo 3. Permission issues
    echo.
    echo Try running as administrator or check your internet connection.
    pause
    exit /b 1
)

echo.
echo Installation completed successfully!
echo.
echo To start the development server, run:
echo   npm run dev
echo.
echo To build for production, run:
echo   npm run build
echo.
pause
