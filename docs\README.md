# PhantomSDR-Plus Documentation

Welcome to the PhantomSDR-Plus documentation. This comprehensive guide covers everything from installation to advanced development.

## 📚 Documentation Overview

### [Technical Documentation](TECHNICAL_DOCUMENTATION.md)
The main comprehensive guide covering:
- System requirements and installation
- Configuration and setup
- Architecture overview
- Hardware support
- User interface guide
- Performance optimization
- Troubleshooting

**Start here if you're new to PhantomSDR-Plus**

### [API Reference](API_REFERENCE.md)
Detailed API documentation including:
- WebSocket endpoints
- Message protocols
- Binary data formats
- Client/server communication
- Code examples in multiple languages

**Essential for developers building integrations**

### [Development Guide](DEVELOPMENT_GUIDE.md)
Complete development guide covering:
- Setting up development environment
- Project structure and architecture
- Adding new features
- Backend and frontend development
- Testing and debugging
- Contributing guidelines

**For contributors and developers extending PhantomSDR-Plus**

---

## 🚀 Quick Start

### Installation

```bash
# Clone repository
git clone --recursive https://github.com/Steven9101/PhantomSDR-Plus.git
cd PhantomSDR-Plus

# Run automatic installation
chmod +x *.sh
sudo ./install.sh
```

### Basic Configuration

Create `config.toml`:

```toml
[server]
port = 9002
html_root = "frontend/dist/"

[input]
sps = 2880000        # Sample rate
fft_size = 131072    # FFT size
frequency = 145000000 # Center frequency
signal = "iq"        # Signal type

[input.driver]
name = "stdin"
format = "u8"        # RTL-SDR format
```

### Running

```bash
# With RTL-SDR
rtl_sdr -f 145000000 -s 2880000 - | ./build/spectrumserver --config config.toml

# Open browser
http://localhost:9002
```

---

## 📋 Documentation Index

### Installation & Setup
- [System Requirements](TECHNICAL_DOCUMENTATION.md#12-system-requirements)
- [Installation Guide](TECHNICAL_DOCUMENTATION.md#2-installation-guide)
- [Configuration](TECHNICAL_DOCUMENTATION.md#3-configuration)
- [Hardware Support](TECHNICAL_DOCUMENTATION.md#6-hardware-support)

### Architecture & Design
- [System Architecture](TECHNICAL_DOCUMENTATION.md#4-system-architecture)
- [Backend Components](TECHNICAL_DOCUMENTATION.md#41-backend-components)
- [Frontend Components](TECHNICAL_DOCUMENTATION.md#42-frontend-components)
- [Data Flow](TECHNICAL_DOCUMENTATION.md#43-data-flow)

### API & Protocol
- [WebSocket Endpoints](API_REFERENCE.md#1-websocket-endpoints)
- [Message Formats](API_REFERENCE.md#2-message-protocol)
- [Binary Protocols](API_REFERENCE.md#3-binary-data-formats)
- [Client Commands](API_REFERENCE.md#4-client-commands)

### Development
- [Environment Setup](DEVELOPMENT_GUIDE.md#1-development-environment-setup)
- [Project Structure](DEVELOPMENT_GUIDE.md#2-project-structure)
- [Adding Features](DEVELOPMENT_GUIDE.md#3-adding-new-features)
- [Testing & Debugging](DEVELOPMENT_GUIDE.md#6-testing-and-debugging)

### User Guide
- [Web Interface](TECHNICAL_DOCUMENTATION.md#8-user-interface)
- [Controls](TECHNICAL_DOCUMENTATION.md#82-controls-and-features)
- [Keyboard Shortcuts](TECHNICAL_DOCUMENTATION.md#83-keyboard-shortcuts)

### Performance & Optimization
- [CPU Optimization](TECHNICAL_DOCUMENTATION.md#91-cpu-optimization)
- [GPU Acceleration](TECHNICAL_DOCUMENTATION.md#92-gpu-acceleration)
- [Network Optimization](TECHNICAL_DOCUMENTATION.md#93-network-optimization)

### Troubleshooting
- [Common Issues](TECHNICAL_DOCUMENTATION.md#101-common-issues)
- [Performance Problems](TECHNICAL_DOCUMENTATION.md#102-performance-problems)
- [Debug Mode](TECHNICAL_DOCUMENTATION.md#103-debug-mode)

---

## 📊 Key Features

### Core Features
- ✅ **Multi-user Support** - 100+ concurrent users
- ✅ **High Sample Rates** - Up to 70 MSPS
- ✅ **GPU Acceleration** - CUDA and OpenCL support
- ✅ **Real-time Processing** - Low latency waterfall and audio
- ✅ **Advanced Compression** - ZSTD and FLAC/Opus
- ✅ **Built-in Chat** - Real-time communication
- ✅ **Auto Registration** - SDR list integration

### Supported Hardware
- RTL-SDR (all variants)
- HackRF One
- Airspy HF+
- SDRplay RSP1A
- RX888 MK2
- Any device with rx_tools support

### Demodulation Modes
- USB (Upper Sideband)
- LSB (Lower Sideband)
- AM (Amplitude Modulation)
- FM (Frequency Modulation)

---

## 🔧 Technical Stack

### Backend
- **Language**: C++23
- **Build System**: Meson
- **FFT**: FFTW3, CUDA, OpenCL
- **WebSocket**: websocketpp
- **Compression**: ZSTD, FLAC, Opus
- **JSON**: nlohmann/json, glaze

### Frontend
- **Framework**: Svelte
- **Build Tool**: Vite
- **Language**: JavaScript
- **WebAssembly**: Custom DSP modules
- **Styling**: Tailwind CSS

---

## 📈 Performance Benchmarks

| Configuration | CPU Usage | GPU Usage | Users | Bandwidth |
|--------------|-----------|-----------|-------|-----------|
| RTL-SDR 2.88 MSPS | 15% | - | 50 | 10 Mbps |
| RX888 64 MSPS (CPU) | 40% | - | 50 | 45 Mbps |
