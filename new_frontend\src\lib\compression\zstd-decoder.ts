/**
 * ZSTD Decompression Module
 * Handles ZSTD decompression for waterfall data with WebAssembly acceleration
 */

import { WaterfallError, WaterfallErrorType } from '@/types/waterfall';

// ZSTD WebAssembly module interface
interface ZstdModule {
  ZSTD_decompress: (dst: number, dstSize: number, src: Uint8Array, srcSize: number) => number;
  _malloc: (size: number) => number;
  _free: (ptr: number) => void;
  HEAPU8: Uint8Array;
}

/**
 * High-performance ZSTD decoder with WebAssembly acceleration
 * Maintains compatibility with PhantomSDR-Plus backend compression
 */
export class ZstdDecoder {
  private module: ZstdModule | null = null;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;
  private decompressBuffer: number = 0;
  private readonly maxDecompressSize = 1024 * 1024; // 1MB buffer

  constructor() {
    this.initPromise = this.initialize();
  }

  /**
   * Initialize the ZSTD WebAssembly module
   */
  private async initialize(): Promise<void> {
    try {
      // Load ZSTD WebAssembly module
      // This would typically load from a CDN or local file
      const moduleFactory = await import('zstd-codec');
      this.module = await moduleFactory.default();
      
      // Allocate persistent decompression buffer
      this.decompressBuffer = this.module._malloc(this.maxDecompressSize);
      
      this.isInitialized = true;
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.CONFIGURATION_ERROR,
        'Failed to initialize ZSTD decoder',
        error
      );
    }
  }

  /**
   * Ensure the decoder is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized && this.initPromise) {
      await this.initPromise;
    }
    
    if (!this.module) {
      throw new WaterfallError(
        WaterfallErrorType.CONFIGURATION_ERROR,
        'ZSTD module not initialized'
      );
    }
  }

  /**
   * Decompress ZSTD-compressed waterfall data
   * @param compressedData - Compressed data from WebSocket
   * @returns Decompressed power values as Int8Array
   */
  async decompress(compressedData: Uint8Array): Promise<Int8Array> {
    await this.ensureInitialized();
    
    if (!this.module) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'ZSTD module not available'
      );
    }

    try {
      // Perform decompression
      const decompressedSize = this.module.ZSTD_decompress(
        this.decompressBuffer,
        this.maxDecompressSize,
        compressedData,
        compressedData.length
      );

      if (decompressedSize <= 0) {
        throw new Error(`ZSTD decompression failed with code: ${decompressedSize}`);
      }

      // Copy decompressed data from WASM memory
      const result = new Int8Array(decompressedSize);
      const sourceView = new Int8Array(
        this.module.HEAPU8.buffer,
        this.decompressBuffer,
        decompressedSize
      );
      result.set(sourceView);

      return result;
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'ZSTD decompression failed',
        error
      );
    }
  }

  /**
   * Get decompression statistics
   */
  getStats(): { isInitialized: boolean; bufferSize: number } {
    return {
      isInitialized: this.isInitialized,
      bufferSize: this.maxDecompressSize,
    };
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.module && this.decompressBuffer) {
      this.module._free(this.decompressBuffer);
      this.decompressBuffer = 0;
    }
    this.module = null;
    this.isInitialized = false;
  }
}

/**
 * Fallback JavaScript ZSTD decoder for environments without WebAssembly
 * Uses a pure JavaScript implementation with reduced performance
 */
export class ZstdDecoderFallback {
  private decoder: any = null;
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // Use a pure JavaScript ZSTD implementation as fallback
      const { ZstdCodec } = await import('zstd-codec');
      this.decoder = new ZstdCodec();
      this.isInitialized = true;
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.CONFIGURATION_ERROR,
        'Failed to initialize fallback ZSTD decoder',
        error
      );
    }
  }

  async decompress(compressedData: Uint8Array): Promise<Int8Array> {
    if (!this.isInitialized || !this.decoder) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'Fallback ZSTD decoder not initialized'
      );
    }

    try {
      const decompressed = this.decoder.decompress(compressedData);
      return new Int8Array(decompressed);
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'Fallback ZSTD decompression failed',
        error
      );
    }
  }

  getStats(): { isInitialized: boolean; type: string } {
    return {
      isInitialized: this.isInitialized,
      type: 'fallback',
    };
  }

  destroy(): void {
    this.decoder = null;
    this.isInitialized = false;
  }
}

/**
 * Factory function to create the appropriate ZSTD decoder
 * Automatically selects WebAssembly or fallback implementation
 */
export async function createZstdDecoder(): Promise<ZstdDecoder | ZstdDecoderFallback> {
  // Check for WebAssembly support
  if (typeof WebAssembly !== 'undefined' && WebAssembly.instantiate) {
    try {
      return new ZstdDecoder();
    } catch (error) {
      console.warn('WebAssembly ZSTD decoder failed, falling back to JavaScript implementation');
    }
  }

  return new ZstdDecoderFallback();
}
