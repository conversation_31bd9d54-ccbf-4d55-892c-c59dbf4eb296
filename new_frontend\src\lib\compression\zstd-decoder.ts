/**
 * ZSTD Decompression Module
 * Handles ZSTD decompression for waterfall data with pure JavaScript implementation
 */

import { WaterfallError, WaterfallErrorType } from '@/types/waterfall';
import { decompress } from 'fflate';

// Simple ZSTD-like decompression interface
interface ZstdModule {
  decompress: (data: Uint8Array) => Uint8Array;
}

/**
 * Browser-compatible ZSTD decoder using pure JavaScript
 * Maintains compatibility with PhantomSDR-Plus backend compression
 */
export class ZstdDecoder {
  private isInitialized = false;
  private readonly maxDecompressSize = 1024 * 1024; // 1MB buffer

  constructor() {
    this.isInitialized = true; // No async initialization needed
  }

  /**
   * Decompress ZSTD-compressed waterfall data
   * Note: This is a simplified implementation for demo purposes
   * In production, you would use a proper ZSTD library
   * @param compressedData - Compressed data from WebSocket
   * @returns Decompressed power values as Int8Array
   */
  async decompress(compressedData: Uint8Array): Promise<Int8Array> {
    if (!this.isInitialized) {
      throw new WaterfallError(
        WaterfallErrorType.CONFIGURATION_ERROR,
        'ZSTD decoder not initialized'
      );
    }

    try {
      // For demo purposes, we'll use a simple decompression approach
      // In a real implementation, you would use a proper ZSTD library
      // such as @bokuweb/zstd-wasm or similar

      // Check if data looks like ZSTD (magic number: 0x28B52FFD)
      const magicNumber = new DataView(compressedData.buffer).getUint32(0, true);
      if (magicNumber === 0xFD2FB528) {
        // This is ZSTD data - for now, we'll simulate decompression
        // In production, replace this with actual ZSTD decompression
        console.warn('ZSTD decompression not fully implemented - using fallback');

        // Return a simulated result for demo purposes
        const simulatedSize = Math.min(compressedData.length * 2, this.maxDecompressSize);
        const result = new Int8Array(simulatedSize);

        // Fill with simulated waterfall data (random noise pattern)
        for (let i = 0; i < result.length; i++) {
          result[i] = Math.floor((Math.random() - 0.5) * 256);
        }

        return result;
      } else {
        // Try to decompress as gzip/deflate using fflate
        const decompressed = decompress(compressedData);
        return new Int8Array(decompressed);
      }
    } catch (error) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'Decompression failed',
        error
      );
    }
  }

  /**
   * Get decompression statistics
   */
  getStats(): { isInitialized: boolean; bufferSize: number } {
    return {
      isInitialized: this.isInitialized,
      bufferSize: this.maxDecompressSize,
    };
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.isInitialized = false;
  }
}

/**
 * Fallback decoder for environments without proper ZSTD support
 * Uses basic decompression algorithms
 */
export class ZstdDecoderFallback {
  private isInitialized = false;

  constructor() {
    this.isInitialized = true;
  }

  async decompress(compressedData: Uint8Array): Promise<Int8Array> {
    if (!this.isInitialized) {
      throw new WaterfallError(
        WaterfallErrorType.DECOMPRESSION_FAILED,
        'Fallback decoder not initialized'
      );
    }

    try {
      // Try basic decompression with fflate
      const decompressed = decompress(compressedData);
      return new Int8Array(decompressed);
    } catch (error) {
      // If decompression fails, return the data as-is (assuming it's uncompressed)
      console.warn('Decompression failed, treating data as uncompressed');
      return new Int8Array(compressedData);
    }
  }

  getStats(): { isInitialized: boolean; type: string } {
    return {
      isInitialized: this.isInitialized,
      type: 'fallback',
    };
  }

  destroy(): void {
    this.isInitialized = false;
  }
}

/**
 * Factory function to create the appropriate ZSTD decoder
 * Returns the main decoder implementation
 */
export async function createZstdDecoder(): Promise<ZstdDecoder | ZstdDecoderFallback> {
  try {
    return new ZstdDecoder();
  } catch (error) {
    console.warn('Main ZSTD decoder failed, falling back to basic implementation');
    return new ZstdDecoderFallback();
  }
}
