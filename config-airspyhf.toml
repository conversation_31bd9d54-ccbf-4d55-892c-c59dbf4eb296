[server]
port=81 # Server port
html_root="frontend/dist/" # HTML files to be hosted
otherusers=1 # Send where other users are listening, 0 to disable
threads=2

[websdr]
register_online=false # If the SDR should be registered on https://sdr-list.xyz then put it to true
name="ChangeThis" # Name that is shown on https://sdr-list.xyz
antenna="ChangeThis" # Antenna that is shown on https://sdr-list.xyz
grid_locator="ChangeThis" # 4 or 6 length Grid Locatlr shown on https://sdr-list.xyz and for the Distance of FT8 Signals
hostname="" # If you use ddns or something to host with a domain enter it here for https://sdr-list.xyz

[input]
sps=912000 # Input Sample Rate
fft_size=131072 # FFT bins
fft_threads=2
brightness_offset=-6 # Waterfall brightness offset. Reduce to negative if you see black regions in the waterfall
frequency=456000 # Baseband frequency
signal="iq" # real or iq
accelerator="opencl" # Accelerator: none, cuda, opencl
audio_sps=44100 # Audio Sample Rate
smeter_offset=-12

[input.driver]
name="stdin" # Driver name
format="s16" # Sample format: u8, s8, u16, s16, u32, s32, f32, f64

[input.defaults]
frequency=648000 # Default frequency to show user
modulation="AM" # Default modulation
