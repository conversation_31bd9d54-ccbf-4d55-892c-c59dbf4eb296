# PhantomSDR-Plus Technical Documentation

![PhantomSDR-Plus Logo](websdr.PNG)

**Version:** 1.0.0  
**Last Updated:** June 2025  
**Repository:** https://github.com/Steven9101/PhantomSDR-Plus

---

## Executive Summary

PhantomSDR-Plus is a high-performance WebSDR (Web-based Software Defined Radio) application that transforms SDR hardware into a multi-user web-accessible radio receiver. Built with C++23 backend and modern web technologies, it delivers real-time spectrum visualization, waterfall display, and audio demodulation to hundreds of simultaneous users through standard web browsers.

### Key Features

- **Multi-user Support**: Handles 100+ concurrent users with minimal CPU overhead
- **High Sample Rates**: Processes up to 70 MSPS real / 35 MSPS IQ data streams  
- **GPU Acceleration**: Optional CUDA and OpenCL support for enhanced FFT performance
- **Real-time Processing**: Low-latency waterfall display and audio streaming
- **Advanced Compression**: ZSTD for waterfall data, FLAC/Opus for audio streams
- **Built-in Chat System**: Real-time user communication
- **Automatic SDR Registration**: Integration with https://sdr-list.xyz
- **Band Plan Visualization**: Frequency allocations displayed on waterfall

---

## Table of Contents

1. [Introduction](#1-introduction)
   - 1.1 [Overview](#11-overview)
   - 1.2 [System Requirements](#12-system-requirements)
   - 1.3 [Architecture Overview](#13-architecture-overview)

2. [Installation Guide](#2-installation-guide)
   - 2.1 [Prerequisites](#21-prerequisites)
   - 2.2 [Automatic Installation](#22-automatic-installation)
   - 2.3 [Manual Installation](#23-manual-installation)
   - 2.4 [GPU Support](#24-gpu-support)

3. [Configuration](#3-configuration)
   - 3.1 [Configuration File Structure](#31-configuration-file-structure)
   - 3.2 [Server Settings](#32-server-settings)
   - 3.3 [Input Configuration](#33-input-configuration)
   - 3.4 [WebSDR Registration](#34-websdr-registration)

4. [System Architecture](#4-system-architecture)
   - 4.1 [Backend Components](#41-backend-components)
   - 4.2 [Frontend Components](#42-frontend-components)
   - 4.3 [Data Flow](#43-data-flow)
   - 4.4 [WebSocket Protocol](#44-websocket-protocol)

5. [API Reference](#5-api-reference)
   - 5.1 [WebSocket Endpoints](#51-websocket-endpoints)
   - 5.2 [Message Formats](#52-message-formats)
   - 5.3 [Binary Protocol](#53-binary-protocol)

6. [Hardware Support](#6-hardware-support)
   - 6.1 [Supported Devices](#61-supported-devices)
   - 6.2 [Device Configuration](#62-device-configuration)
   - 6.3 [Performance Considerations](#63-performance-considerations)

7. [Development Guide](#7-development-guide)
   - 7.1 [Building from Source](#71-building-from-source)
   - 7.2 [Code Structure](#72-code-structure)
   - 7.3 [Adding New Features](#73-adding-new-features)

8. [User Interface](#8-user-interface)
   - 8.1 [Web Interface Overview](#81-web-interface-overview)
   - 8.2 [Controls and Features](#82-controls-and-features)
   - 8.3 [Keyboard Shortcuts](#83-keyboard-shortcuts)

9. [Performance Optimization](#9-performance-optimization)
   - 9.1 [CPU Optimization](#91-cpu-optimization)
   - 9.2 [GPU Acceleration](#92-gpu-acceleration)
   - 9.3 [Network Optimization](#93-network-optimization)

10. [Troubleshooting](#10-troubleshooting)
    - 10.1 [Common Issues](#101-common-issues)
    - 10.2 [Performance Problems](#102-performance-problems)
    - 10.3 [Debug Mode](#103-debug-mode)

11. [Security](#11-security)
    - 11.1 [Network Security](#111-network-security)
    - 11.2 [User Limits](#112-user-limits)
    - 11.3 [Chat Moderation](#113-chat-moderation)

12. [Code Examples](#12-code-examples)
    - 12.1 [WebSocket Client](#121-websocket-client)
    - 12.2 [Custom Integrations](#122-custom-integrations)

13. [Appendices](#13-appendices)
    - A. [Configuration Examples](#a-configuration-examples)
    - B. [Performance Benchmarks](#b-performance-benchmarks)
    - C. [Glossary](#c-glossary)

---

## 1. Introduction

### 1.1 Overview

PhantomSDR-Plus is a fork of the original PhantomSDR project, focusing exclusively on Linux support to deliver enhanced features and performance. The system consists of:

- **Spectrum Server**: High-performance C++23 backend handling SDR data processing
- **Web Frontend**: Svelte-based interface with WebSocket real-time communication
- **FFT Engine**: Multi-threaded FFT processing with optional GPU acceleration
- **Compression System**: Efficient data compression for bandwidth optimization

### 1.2 System Requirements

#### Minimum Requirements
- **OS**: Ubuntu 22.04 LTS or Debian Bookworm
- **CPU**: Intel Core i5 or AMD Ryzen 5 (4+ cores)
- **RAM**: 8 GB
- **Network**: 100 Mbps
- **SDR**: RTL-SDR or compatible

#### Recommended Requirements
- **OS**: Ubuntu 22.04 LTS
- **CPU**: Intel Core i7/i9 or AMD Ryzen 7/9 (8+ cores)
- **RAM**: 16 GB or more
- **GPU**: NVIDIA with CUDA or AMD/Intel with OpenCL
- **Network**: 1 Gbps
- **SDR**: RX888 MK2 or Airspy HF+

**Important**: Ubuntu 24.04 is NOT supported due to compilation issues.

### 1.3 Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    PhantomSDR-Plus System                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌──────────┐    ┌─────────────┐    ┌──────────────────┐  │
│  │   SDR    │───▶│  Spectrum   │───▶│  Web Clients     │  │
│  │ Hardware │    │   Server    │    │  (Browsers)      │  │
│  └──────────┘    └─────────────┘    └──────────────────┘  │
│       │                 │                     │             │
│       │                 │                     │             │
│       ▼                 ▼                     ▼             │
│  ┌──────────┐    ┌─────────────┐    ┌──────────────────┐  │
│  │  Sample  │    │     FFT     │    │   WebSocket      │  │
│  │  Reader  │    │ Processing  │    │   Connections    │  │
│  └──────────┘    └─────────────┘    └──────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Installation Guide

### 2.1 Prerequisites

#### Ubuntu 22.04 Prerequisites

```bash
sudo apt update
sudo apt install -y \
    build-essential \
    cmake \
    pkg-config \
    meson \
    libfftw3-dev \
    libwebsocketpp-dev \
    libflac++-dev \
    zlib1g-dev \
    libzstd-dev \
    libboost-all-dev \
    libopus-dev \
    libliquid-dev \
    git \
    psmisc \
    libcurl4-openssl-dev
```

#### Fedora Prerequisites

```bash
sudo dnf install -y \
    g++ \
    meson \
    cmake \
    fftw3-devel \
    websocketpp-devel \
    flac-devel \
    zlib-devel \
    boost-devel \
    libzstd-devel \
    opus-devel \
    liquid-dsp-devel \
    git \
    psmisc \
    libcurl-devel
```

### 2.2 Automatic Installation

```bash
# Clone repository
git clone --recursive https://github.com/Steven9101/PhantomSDR-Plus.git
cd PhantomSDR-Plus

# Make scripts executable
chmod +x *.sh

# Run installation
sudo ./install.sh
```

**Note**: Restart your terminal after installation.

### 2.3 Manual Installation

```bash
# Clone repository
git clone --recursive https://github.com/Steven9101/PhantomSDR-Plus.git
cd PhantomSDR-Plus

# Configure build
meson setup build --buildtype=release --optimization=3

# Compile
meson compile -C build
```

### 2.4 GPU Support

#### CUDA Support

```bash
# Install CUDA Toolkit
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt update
sudo apt install -y cuda-toolkit-12-0

# Enable CUDA in build
meson configure build -Dcuda=enabled
```

#### OpenCL Support

```bash
# AMD GPUs
sudo apt install -y rocm-opencl-runtime

# Intel GPUs  
sudo apt install -y intel-opencl-icd

# Common packages
sudo apt install -y opencl-headers ocl-icd-opencl-dev clinfo

# Enable OpenCL in build
meson configure build -Dopencl=enabled
```

---

## 3. Configuration

### 3.1 Configuration File Structure

PhantomSDR-Plus uses TOML format for configuration. Create `config.toml` in the application directory:

```toml
[server]
port = 9002
html_root = "frontend/dist/"
otherusers = 1
threads = 8

[websdr]
register_online = false
name = "My WebSDR"
antenna = "Discone @ 20m"
grid_locator = "JO21"
hostname = "mysdr.example.com"

[input]
sps = 64000000
fft_size = 131072
frequency = 15000000
signal = "real"
accelerator = "none"
audio_sps = 12000
brightness_offset = 0
smeter_offset = 0

[input.driver]
name = "stdin"
format = "s16"

[input.defaults]
frequency = 14074000
modulation = "USB"

[limits]
audio = 100
waterfall = 100
events = 100
```

### 3.2 Server Settings

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `port` | integer | 9002 | WebSocket server port |
| `html_root` | string | "frontend/dist/" | Static file directory |
| `otherusers` | integer | 1 | Show other users (0=disable) |
| `threads` | integer | 1 | Worker threads |

### 3.3 Input Configuration

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `sps` | integer | required | Sample rate in Hz |
| `fft_size` | integer | 131072 | FFT size (power of 2) |
| `frequency` | integer | required | Center frequency in Hz |
| `signal` | string | required | "real" or "iq" |
| `accelerator` | string | "none" | "none", "cuda", "opencl" |
| `audio_sps` | integer | 12000 | Audio sample rate |
| `brightness_offset` | integer | 0 | Waterfall brightness adjustment |
| `smeter_offset` | integer | 0 | S-meter calibration |

#### Sample Formats

| Format | Description | Bytes/Sample |
|--------|-------------|--------------|
| `u8` | Unsigned 8-bit | 1 |
| `s8` | Signed 8-bit | 1 |
| `u16` | Unsigned 16-bit | 2 |
| `s16` | Signed 16-bit | 2 |
| `u32` | Unsigned 32-bit | 4 |
| `s32` | Signed 32-bit | 4 |
| `f32` | 32-bit float | 4 |
| `f64` | 64-bit double | 8 |

### 3.4 WebSDR Registration

When `register_online = true`, the server registers with https://sdr-list.xyz every 10 seconds:

```toml
[websdr]
register_online = true
name = "Berlin WebSDR"
antenna = "Diamond X-300"
grid_locator = "JO62QL"
hostname = "berlin.example.com"
```

---

## 4. System Architecture

### 4.1 Backend Components

#### Core Components

1. **SpectrumServer** ([`src/spectrumserver.cpp`](src/spectrumserver.cpp))
   - Main application class
   - Manages WebSocket connections
   - Coordinates all subsystems

2. **SampleReader** ([`src/samplereader.cpp`](src/samplereader.cpp))
   - Reads samples from stdin
   - Converts sample formats
   - Buffers incoming data

3. **FFT Engine** ([`src/fft.cpp`](src/fft.cpp))
   - Performs Fast Fourier Transforms
   - Supports FFTW3, CUDA, OpenCL backends
   - Multi-threaded processing

4. **Client Classes**
   - `AudioClient` ([`src/signal.cpp`](src/signal.cpp)) - Audio streaming
   - `WaterfallClient` ([`src/waterfall.cpp`](src/waterfall.cpp)) - Waterfall data
   - `ChatClient` ([`src/chat.cpp`](src/chat.cpp)) - Chat system

5. **Compression** ([`src/compression.cpp`](src/compression.cpp))
   - ZSTD for waterfall data
   - FLAC/Opus for audio

### 4.2 Frontend Components

1. **Main Application** ([`frontend/src/App.svelte`](frontend/src/App.svelte))
   - Root Svelte component
   - State management

2. **WebSocket Clients**
   - [`frontend/src/audio.js`](frontend/src/audio.js) - Audio handling
   - [`frontend/src/waterfall.js`](frontend/src/waterfall.js) - Waterfall display
   - [`frontend/src/events.js`](frontend/src/events.js) - Event stream

3. **UI Components**
   - Waterfall canvas rendering
   - Spectrum display
   - Frequency controls
   - Chat interface

### 4.3 Data Flow

```
SDR Hardware
     │
     ▼ (Raw samples via stdin)
SampleReader
     │
     ▼ (Float samples)
FFT Engine
     │
     ├──► Waterfall Generator
     │         │
     │         ▼ (Compressed waterfall)
     │    WaterfallClient
     │
     └──► Signal Processor
              │
              ▼ (Audio slices)
         AudioClient
              │
              ▼ (Compressed audio)
         Web Browser
```

### 4.4 WebSocket Protocol

#### Endpoints

| Endpoint | Purpose |
|----------|---------|
| `/audio` | Audio streaming |
| `/waterfall` | Waterfall data |
| `/events` | Statistics and user info |
| `/chat` | Chat messages |

---

## 5. API Reference

### 5.1 WebSocket Endpoints

#### Audio Endpoint (`/audio`)

Initial connection receives configuration:

```json
{
  "sps": 64000000,
  "audio_max_sps": 12000,
  "audio_max_fft": 512,
  "fft_size": 131072,
  "fft_result_size": 65536,
  "waterfall_size": 1024,
  "basefreq": 0,
  "total_bandwidth": 32000000,
  "defaults": {
    "frequency": 14074000,
    "modulation": "USB",
    "l": 456,
    "m": 457.5,
    "r": 460
  },
  "waterfall_compression": "zstd",
  "audio_compression": "flac",
  "grid_locator": "JO21",
  "smeter_offset": 0,
  "markers": "..."
}
```

Audio data packets use CBOR encoding with FLAC/Opus compressed audio.

#### Waterfall Endpoint (`/waterfall`)

Receives same initial configuration, then binary waterfall data packets with header:

| Offset | Size | Description |
|--------|------|-------------|
| 0 | 4 | Frame number |
| 4 | 4 | Left frequency index |
| 8 | 4 | Right frequency index |
| 12 | N | ZSTD compressed data |

#### Events Endpoint (`/events`)

Receives periodic JSON updates:

```json
{
  "waterfall_clients": 35,
  "signal_clients": 18,
  "signal_changes": {
    "user123": [456, 457.5, 460]
  },
  "waterfall_kbits": 8960.5,
  "audio_kbits": 2304.2
}
```

#### Chat Endpoint (`/chat`)

Text messages with timestamp and filtering:

```
2025-06-13 15:30:45 User123: Hello everyone!
```

### 5.2 Message Formats

#### Client to Server

##### Window Configuration

```json
{
  "cmd": "window",
  "l": 456,      // Left edge
  "m": 457.5,    // Center (optional)
  "r": 460,      // Right edge
  "level": 0     // Zoom level (optional)
}
```

##### Demodulation Mode

```json
{
  "cmd": "demodulation",
  "demodulation": "USB"  // USB, LSB, AM, FM
}
```

##### User ID

```json
{
  "cmd": "userid",
  "userid": "unique_user_id"
}
```

##### Mute Control

```json
{
  "cmd": "mute",
  "mute": true
}
```

##### Chat Message

```json
{
  "cmd": "chat",
  "username": "User123",
  "message": "Hello!"
}
```

### 5.3 Binary Protocol

#### Waterfall Data Format

Waterfall data is sent as binary packets:

1. **Header** (12 bytes)
   - Frame number (uint32)
   - Left index (int32)
   - Right index (int32)

2. **Data** (variable length)
   - ZSTD compressed power values
   - Each value is int8_t representing dB

#### Audio Data Format

Audio uses CBOR encoding:

```javascript
{
  pwr: -73.5,  // Signal power in dB
  data: Uint8Array  // Compressed audio
}
```

---

## 6. Hardware Support

### 6.1 Supported Devices

#### RTL-SDR

```bash
rtl_sdr -f 145000000 -s 2880000 - | ./build/spectrumserver --config config.toml
```

#### HackRF

```bash
rx_sdr -f 435000000 -s 20000000 -d driver=hackrf - | ./build/spectrumserver --config config.toml
```

#### Airspy HF+

```bash
./start-airspyhf.sh
```

#### SDRplay RSP1A

```bash
./start-rsp1a.sh
```

#### RX888 MK2

```bash
./start-rx888mk2.sh
```

### 6.2 Device Configuration

Example configurations are provided:
- `config.example.rtlsdr.toml`
- `config.example.hackrf.toml`
- `config-airspyhf.toml`
- `config-rsp1a.toml`
- `config-rx888mk2.toml`

### 6.3 Performance Considerations

| Device | Sample Rate | CPU Usage | GPU Usage |
|--------|-------------|-----------|-----------|
| RTL-SDR | 2.88 MSPS | 5-10% | N/A |
| HackRF | 20 MSPS | 20-25% | N/A |
| RX888 MK2 | 64 MSPS | 38-40% | N/A |
| RX888 MK2 + CUDA | 64 MSPS | 10-15% | 25-30% |

---

## 7. Development Guide

### 7.1 Building from Source

#### Debug Build

```bash
meson setup builddir --buildtype=debug
meson compile -C builddir
```

#### Development Tools

```bash
sudo apt install -y gdb valgrind clang-format clang-tidy
```

### 7.2 Code Structure

```
PhantomSDR-Plus/
├── src/                    # C++ backend
│   ├── spectrumserver.cpp  # Main server
│   ├── client.cpp/h        # Client base classes
│   ├── websocket.cpp/h     # WebSocket handling
│   ├── fft.cpp/h          # FFT processing
│   ├── waterfall.cpp/h    # Waterfall generation
│   ├── signal.cpp/h       # Signal processing
│   ├── audio.cpp/h        # Audio encoding
│   ├── chat.cpp/h         # Chat system
│   └── utils/             # Utility functions
├── frontend/              # Web frontend
│   ├── src/
│   │   ├── App.svelte     # Main component
│   │   ├── audio.js       # Audio handling
│   │   ├── waterfall.js   # Waterfall display
│   │   └── events.js      # Event handling
│   └── dist/              # Built frontend
└── docs/                  # Documentation
```

### 7.3 Adding New Features

#### Adding a New WebSocket Endpoint

1. Add handler in `on_open()` ([`src/websocket.cpp`](src/websocket.cpp)):

```cpp
if (path == "/newfeature") {
    on_open_newfeature(hdl);
}
```

2. Create client class inheriting from `Client`
3. Implement message handling
4. Add frontend WebSocket client

#### Adding a New Demodulation Mode

1. Add enum value in [`src/client.h`](src/client.h)
2. Implement demodulation in [`src/signal.cpp`](src/signal.cpp)
3. Add frontend UI option

---

## 8. User Interface

### 8.1 Web Interface Overview

The web interface consists of:
- **Waterfall Display**: Time-frequency visualization
- **Spectrum Display**: Real-time FFT
- **Frequency Controls**: Tuning and mode selection
- **Audio Controls**: Volume, squelch, filters
- **Chat Panel**: User communication

### 8.2 Controls and Features

#### Mouse Controls
- **Click**: Tune to frequency
- **Drag**: Select frequency range
- **Scroll**: Zoom in/out
- **Right-click**: Context menu

#### Frequency Input
- Direct entry: `14.205` or `14205000`
- Click tuning on waterfall
- Drag selection for passband

#### Demodulation Modes
- **USB**: Upper sideband (default)
- **LSB**: Lower sideband
- **AM**: Amplitude modulation
- **FM**: Frequency modulation

### 8.3 Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `←/→` | Tune up/down |
| `↑/↓` | Change step size |
| `+/-` | Zoom in/out |
| `Space` | Center on signal |
| `M` | Toggle mute |

---

## 9. Performance Optimization

### 9.1 CPU Optimization

```toml
[server]
threads = 8  # Match CPU cores

[input]
fft_threads = 4  # Parallel FFT
```

Compiler optimizations:
```bash
meson configure build -Doptimization=3 -Db_lto=true
```

### 9.2 GPU Acceleration

#### CUDA Configuration

```toml
[input]
accelerator = "cuda"
```

Performance improvement: ~60% CPU reduction

#### OpenCL Configuration

```toml
[input]
accelerator = "opencl"
```

### 9.3 Network Optimization

```toml
[input]
waterfall_compression = "zstd"  # Efficient compression
audio_compression = "opus"      # Low bitrate audio
```

---

## 10. Troubleshooting

### 10.1 Common Issues

#### Compilation Fails

```bash
# Missing FFTW3
sudo apt install libfftw3-dev

# Missing submodules
git submodule update --init --recursive
```

#### No Audio

- Check browser permissions
- Verify audio sample rate matches SDR capabilities
- Check mute status

#### High CPU Usage

- Enable GPU acceleration
- Reduce FFT size
- Lower sample rate

### 10.2 Performance Problems

#### Slow Waterfall

- Reduce waterfall clients limit
- Enable compression
- Use GPU acceleration

#### Audio Dropouts

- Increase audio buffer size
- Check network bandwidth
- Reduce concurrent users

### 10.3 Debug Mode

```bash
# Enable debug logging
RUST_LOG=debug ./build/spectrumserver --config config.toml

# Use GDB
gdb ./build/spectrumserver
```

---

## 11. Security

### 11.1 Network Security

- Bind to specific interface:
```toml
[server]
host = "127.0.0.1"  # Local only
```

- Use reverse proxy with SSL:
```nginx
location / {
    proxy_pass http://localhost:9002;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
}
```

### 11.2 User Limits

```toml
[limits]
audio = 50      # Max audio clients
waterfall = 100 # Max waterfall clients
events = 200    # Max event clients
```

### 11.3 Chat Moderation

Built-in features:
- Username filtering (blocks admin, root, etc.)
- Profanity filter
- Message length limits (200 chars)
- Rate limiting

---

## 12. Code Examples

### 12.1 WebSocket Client

```javascript
// Connect to PhantomSDR-Plus
const ws = new WebSocket('ws://localhost:9002/waterfall');

ws.onopen = () => {
    // Configure window
    ws.send(JSON.stringify({
        cmd: 'window',
        l: 0,
        r: 1024
    }));
    
    // Set user ID
    ws.send(JSON.stringify({
        cmd: 'userid',
        userid: 'custom_client_123'
    }));
};

ws.onmessage = (event) => {
    if (typeof event.data === 'string') {
        // Initial configuration
        const config = JSON.parse(event.data);
        console.log('FFT Size:', config.fft_size);
        console.log('Sample Rate:', config.sps);
    } else {
        // Binary waterfall data
        processWaterfallData(event.data);
    }
};
```

### 12.2 Custom Integrations

#### Python Client Example

```python
import websocket
import json
import struct

def on_message(ws, message):
    if isinstance(message, str):
        config = json.loads(message)
        print(f"Connected to {config.get('name', 'WebSDR')}")
    else:
        # Binary waterfall data
        frame_num = struct.unpack('I', message[0:4])[0]
        left = struct.unpack('i', message[4:8])[0]
        right = struct.unpack('i', message[8:12])[0]
        # Decompress and process data...

ws = websocket.WebSocketApp("ws://localhost:9002/waterfall",
                            on_message=on_message)
ws.run_forever()
```

---

## 13. Appendices

### A. Configuration Examples

#### High-Performance Configuration

```toml
[server]
port = 9002
threads = 16
html_root = "frontend/dist/"

[input]
sps = 64000000
fft_size = 262144
frequency = 15000000
signal = "real"
accelerator = "cuda"
fft_threads = 8
audio_sps = 48000

[limits]
audio = 200
waterfall = 300
events = 500
```

#### Low-Resource Configuration

```toml
[server]
port = 9002
threads = 2

[input]
sps = 2880000
fft_size = 65536
frequency = 145000000
signal = "iq"
accelerator = "none"
fft_threads = 1
audio_sps = 12000

[limits]
audio = 20
waterfall = 30
events = 50
```

### B. Performance Benchmarks

| Configuration | CPU | GPU | Users | CPU Usage | Network |
|---------------|-----|-----|-------|-----------|---------|
| RTL-SDR 2.88 MSPS | Ryzen 5 2600 | None | 50 | 15% | 10 Mbps |
| RX888 64 MSPS | Ryzen 5 2600 | None | 50 | 40% | 45 Mbps |
| RX888 64 MSPS | i5-6500T | Intel HD 530 | 100 | 12% | 90 Mbps |
| RX888 64 MSPS | Ryzen 7 3700X | RTX 2070 | 200 | 8% | 180 Mbps |

### C. Glossary

| Term | Description |
|------|-------------|
| **FFT** | Fast Fourier Transform - converts time-domain signals to frequency domain |
| **IQ** | In-phase and Quadrature - complex signal representation |
| **MSPS** | Mega Samples Per Second - sampling rate |
| **WebSDR** | Web-based Software Defined Radio |
| **ZSTD** | Zstandard compression algorithm |
| **CBOR** | Concise Binary Object Representation |
| **Waterfall** | Time-frequency visualization display |
| **Passband** | Frequency range selected for demodulation |

---

## Version History

- **1.0.0** - Initial release with core features
- **Future** - Planned: WSPR decoder, more digital modes

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines.

## License

PhantomSDR-Plus is released under the GNU General Public License v3.0.

---

**End of Documentation**