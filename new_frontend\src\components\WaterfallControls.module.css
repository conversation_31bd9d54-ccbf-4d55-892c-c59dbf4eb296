/**
 * Waterfall Controls Styles
 * Apple-inspired control panel design
 */

.container {
  position: absolute;
  top: 60px;
  right: 16px;
  width: 280px;
  background: rgba(28, 28, 30, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.content {
  padding: 16px 20px 20px;
  max-height: 400px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #8e8e93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  gap: 12px;
}

.control:last-child {
  margin-bottom: 0;
}

.control label {
  font-size: 13px;
  color: #ffffff;
  font-weight: 500;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Form Controls */
.select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 6px 8px;
  color: #ffffff;
  font-size: 13px;
  min-width: 120px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.select:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

.slider {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #007aff;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #007aff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 122, 255, 0.3);
}

.value {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
  min-width: 30px;
  text-align: right;
}

.checkbox {
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
}

.checkbox:checked {
  background: #007aff;
  border-color: #007aff;
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 10px;
  font-weight: bold;
}

.checkbox:hover {
  border-color: rgba(255, 255, 255, 0.5);
}

.checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

/* Info Section */
.info {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
}

.infoItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.infoItem:last-child {
  margin-bottom: 0;
}

.infoItem span:first-child {
  color: #8e8e93;
  font-weight: 500;
}

.infoItem span:last-child {
  color: #ffffff;
  font-weight: 600;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

/* Action Button */
.actionButton {
  width: 100%;
  background: #ff453a;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #d70015;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 69, 58, 0.3);
}

.actionButton:active {
  transform: translateY(0);
  background: #bf0711;
}

/* Scrollbar Styling */
.content::-webkit-scrollbar {
  width: 6px;
}

.content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    width: auto;
    border-radius: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .content {
    max-height: 300px;
  }
  
  .control {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .control label {
    justify-content: flex-start;
  }
  
  .slider {
    width: 100%;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  .container {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .header h3 {
    color: #000000;
  }
  
  .section h4 {
    color: #6d6d70;
  }
  
  .control label {
    color: #000000;
  }
  
  .select {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #000000;
  }
  
  .select:hover {
    background: rgba(0, 0, 0, 0.1);
  }
  
  .slider {
    background: rgba(0, 0, 0, 0.1);
  }
  
  .checkbox {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.2);
  }
  
  .value {
    color: #6d6d70;
  }
  
  .info {
    background: rgba(0, 0, 0, 0.05);
  }
  
  .infoItem span:first-child {
    color: #6d6d70;
  }
  
  .infoItem span:last-child {
    color: #000000;
  }
}
