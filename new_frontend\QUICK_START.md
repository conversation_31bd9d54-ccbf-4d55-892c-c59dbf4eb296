# Quick Start Guide

## Prerequisites

- Node.js 18 or later
- PhantomSDR-Plus backend running on port 9002

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Start development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to http://localhost:3000

## Configuration

1. **Copy environment file:**
   ```bash
   copy .env.local.example .env.local
   ```

2. **Edit `.env.local`** to match your PhantomSDR-Plus setup:
   ```env
   NEXT_PUBLIC_WEBSOCKET_URL=ws://your-server:9002/waterfall
   ```

## Troubleshooting

### Common Issues

**"Connection failed"**
- Ensure PhantomSDR-Plus is running
- Check the WebSocket URL in the interface
- Verify firewall settings

**"Performance issues"**
- Try reducing buffer size in settings
- Lower the refresh rate
- Disable GPU acceleration if needed

**"Build errors"**
- Delete `node_modules` and `package-lock.json`
- Run `npm install` again
- Ensure Node.js version is 18+

## Features

- **Real-time waterfall display**
- **Interactive frequency selection**
- **Multiple color schemes**
- **Performance monitoring**
- **Responsive design**

## Controls

- **F**: Toggle fullscreen
- **C**: Toggle controls panel
- **S**: Toggle statistics panel
- **R**: Clear display
- **Click & drag**: Select frequency range

## Production Build

```bash
npm run build
npm start
```

For more detailed information, see the full [README.md](README.md).
