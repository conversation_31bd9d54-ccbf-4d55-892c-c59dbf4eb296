/**
 * Demo Page Styles
 * Clean, modern layout showcasing the waterfall component
 */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

.header {
  text-align: center;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
  margin: 0 0 8px 0;
  font-size: 48px;
  font-weight: 700;
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.header p {
  margin: 0;
  font-size: 18px;
  color: #8e8e93;
  font-weight: 400;
}

.main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Settings Section */
.settings {
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
}

.settingsGrid {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  align-items: center;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.inputGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #8e8e93;
}

.input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

.frequencyDisplay {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 14px;
}

.frequency {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-weight: 600;
  color: #007aff;
  font-size: 18px;
}

/* Waterfall Section */
.waterfallSection {
  margin-bottom: 40px;
}

.waterfall {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

/* Log Section */
.logSection {
  margin-bottom: 40px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.logHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logHeader h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.clearButton {
  background: rgba(255, 69, 58, 0.8);
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clearButton:hover {
  background: #ff453a;
  transform: translateY(-1px);
}

.log {
  max-height: 200px;
  overflow-y: auto;
  padding: 16px 24px 20px;
}

.emptyLog {
  color: #8e8e93;
  font-style: italic;
  text-align: center;
  margin: 20px 0;
}

.logEntry {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 13px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.timestamp {
  color: #8e8e93;
  flex-shrink: 0;
}

.message {
  color: #ffffff;
}

/* Features Section */
.features {
  margin-bottom: 40px;
}

.features h2 {
  text-align: center;
  margin: 0 0 32px 0;
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.feature {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.feature h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.feature p {
  margin: 0;
  color: #8e8e93;
  line-height: 1.5;
}

/* Instructions Section */
.instructions {
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.instructions h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
}

.instructionsList {
  display: grid;
  gap: 12px;
}

.instruction {
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.instruction strong {
  color: #007aff;
  font-weight: 600;
}

/* Footer */
.footer {
  text-align: center;
  padding: 40px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.footer p {
  margin: 8px 0;
  color: #8e8e93;
  font-size: 14px;
}

.footer a {
  color: #007aff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.footer a:hover {
  color: #0056cc;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .waterfall {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 36px;
  }
  
  .header p {
    font-size: 16px;
  }
  
  .main {
    padding: 20px 16px;
  }
  
  .settingsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .frequencyDisplay {
    align-items: flex-start;
  }
  
  .featureGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .feature {
    padding: 20px;
  }
  
  .logHeader {
    padding: 16px 20px 12px;
  }
  
  .log {
    padding: 12px 20px 16px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 24px 16px;
  }
  
  .header h1 {
    font-size: 28px;
  }
  
  .settings,
  .instructions {
    padding: 20px;
  }
  
  .feature {
    padding: 16px;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  .container {
    background: linear-gradient(135deg, #f2f2f7 0%, #ffffff 100%);
    color: #000000;
  }
  
  .header {
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .header p {
    color: #6d6d70;
  }
  
  .settings,
  .instructions {
    background: rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .settings h2,
  .instructions h2,
  .features h2 {
    color: #000000;
  }
  
  .inputGroup label {
    color: #6d6d70;
  }
  
  .input {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #000000;
  }
  
  .logSection {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .logHeader {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .logHeader h3 {
    color: #000000;
  }
  
  .emptyLog {
    color: #6d6d70;
  }
  
  .message {
    color: #000000;
  }
  
  .timestamp {
    color: #6d6d70;
  }
  
  .feature {
    background: rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .feature h3 {
    color: #000000;
  }
  
  .feature p {
    color: #6d6d70;
  }
  
  .instruction {
    background: rgba(255, 255, 255, 0.8);
  }
  
  .footer {
    background: rgba(255, 255, 255, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .footer p {
    color: #6d6d70;
  }
}
