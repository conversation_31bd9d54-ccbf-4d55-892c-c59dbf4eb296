{"name": "phantomsdr-waterfall", "version": "1.0.0", "description": "PhantomSDR-Plus Waterfall Component - React/Next.js Implementation", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "cbor-x": "^1.5.4", "zstd-codec": "^0.1.4", "canvas": "^2.11.2", "lodash.throttle": "^4.1.1", "lodash.debounce": "^4.0.8", "classnames": "^2.3.2"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/lodash.throttle": "^4.1.7", "@types/lodash.debounce": "^4.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0"}, "engines": {"node": ">=18.0.0"}}