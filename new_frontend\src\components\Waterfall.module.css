/**
 * Waterfall Component Styles
 * Apple-inspired design with smooth animations and micro-interactions
 */

.container {
  display: flex;
  flex-direction: column;
  background: #1c1c1e;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container:focus {
  outline: 2px solid #007aff;
  outline-offset: 2px;
}

.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #2c2c2e 0%, #1c1c1e 100%);
  border-bottom: 1px solid #38383a;
}

.title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 13px;
  color: #8e8e93;
  font-weight: 400;
  margin-top: 2px;
  display: block;
}

.headerControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Connection Status */
.connectionStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.statusIndicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.connected .statusIndicator {
  background: #30d158;
  box-shadow: 0 0 8px rgba(48, 209, 88, 0.4);
}

.connecting .statusIndicator {
  background: #ff9f0a;
  animation: pulse 1.5s infinite;
}

.disconnected .statusIndicator {
  background: #8e8e93;
}

.error .statusIndicator {
  background: #ff453a;
}

.statusText {
  color: #ffffff;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Control Buttons */
.controlButton {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  padding: 6px 8px;
  color: #ffffff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.controlButton:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.15);
}

/* Canvas Container */
.canvasContainer {
  position: relative;
  flex: 1;
  background: #000000;
  overflow: hidden;
}

.canvas {
  display: block;
  width: 100%;
  height: 100%;
  cursor: crosshair;
  transition: filter 0.2s ease;
}

.dragging .canvas {
  cursor: col-resize;
}

.canvas:hover {
  filter: brightness(1.05);
}

/* Frequency Scale */
.frequencyScale {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  pointer-events: none;
}

/* Loading State */
.loading {
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loadingText {
  color: #8e8e93;
  font-size: 14px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error {
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
  padding: 32px;
}

.errorIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.errorTitle {
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.errorMessage {
  color: #8e8e93;
  font-size: 14px;
  margin: 0 0 24px 0;
  max-width: 400px;
}

.retryButton {
  background: #007aff;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.retryButton:active {
  transform: translateY(0);
}

/* Shortcuts */
.shortcuts {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid #38383a;
  text-align: center;
}

.shortcuts small {
  color: #8e8e93;
  font-size: 11px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 8px 12px;
  }
  
  .title h2 {
    font-size: 16px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .headerControls {
    gap: 4px;
  }
  
  .controlButton {
    padding: 4px 6px;
    font-size: 12px;
  }
  
  .shortcuts {
    display: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: light) {
  .container {
    background: #f2f2f7;
    color: #000000;
  }
  
  .header {
    background: linear-gradient(135deg, #ffffff 0%, #f2f2f7 100%);
    border-bottom: 1px solid #d1d1d6;
  }
  
  .title h2 {
    color: #000000;
  }
  
  .subtitle {
    color: #6d6d70;
  }
  
  .statusText {
    color: #000000;
  }
  
  .controlButton {
    background: rgba(0, 0, 0, 0.1);
    color: #000000;
  }
  
  .controlButton:hover {
    background: rgba(0, 0, 0, 0.2);
  }
  
  .shortcuts {
    background: rgba(255, 255, 255, 0.8);
    border-top: 1px solid #d1d1d6;
  }
  
  .shortcuts small {
    color: #6d6d70;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .container {
    border: 2px solid #ffffff;
  }
  
  .controlButton {
    border: 1px solid #ffffff;
  }
  
  .canvas {
    border: 1px solid #ffffff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .container {
    box-shadow: none;
    border: 1px solid #000000;
  }
  
  .header {
    background: #ffffff;
    color: #000000;
  }
  
  .controlButton,
  .shortcuts {
    display: none;
  }
}
