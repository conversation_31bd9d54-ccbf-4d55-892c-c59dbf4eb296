/**
 * Waterfall Statistics Component
 * Real-time performance and connection statistics display
 */

'use client';

import React from 'react';
import { WaterfallStats as WaterfallStatsType, WaterfallConfig } from '@/types/waterfall';
import styles from './WaterfallStats.module.css';

interface WaterfallStatsProps {
  stats: WaterfallStatsType;
  renderStats: any;
  config: WaterfallConfig | null;
}

export const WaterfallStats: React.FC<WaterfallStatsProps> = ({
  stats,
  renderStats,
  config,
}) => {
  const formatNumber = (num: number, decimals = 1): string => {
    return num.toFixed(decimals);
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDataRate = (kbps: number): string => {
    if (kbps < 1000) {
      return `${formatNumber(kbps)} kbit/s`;
    } else {
      return `${formatNumber(kbps / 1000)} Mbit/s`;
    }
  };

  const getConnectionStatusColor = (status: string): string => {
    switch (status) {
      case 'connected': return '#30d158';
      case 'connecting': return '#ff9f0a';
      case 'error': return '#ff453a';
      default: return '#8e8e93';
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h3>Statistics</h3>
        <div 
          className={styles.statusDot}
          style={{ backgroundColor: getConnectionStatusColor(stats.connectionStatus) }}
        />
      </div>

      <div className={styles.content}>
        {/* Connection Statistics */}
        <div className={styles.section}>
          <h4>Connection</h4>
          <div className={styles.statGrid}>
            <div className={styles.stat}>
              <span className={styles.label}>Status</span>
              <span className={styles.value} style={{ 
                color: getConnectionStatusColor(stats.connectionStatus) 
              }}>
                {stats.connectionStatus.charAt(0).toUpperCase() + stats.connectionStatus.slice(1)}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.label}>Data Rate</span>
              <span className={styles.value}>
                {formatDataRate(stats.dataRate)}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.label}>Latency</span>
              <span className={styles.value}>
                {formatNumber(stats.averageLatency)} ms
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.label}>Compression</span>
              <span className={styles.value}>
                {formatNumber(stats.compressionRatio, 2)}:1
              </span>
            </div>
          </div>
        </div>

        {/* Frame Statistics */}
        <div className={styles.section}>
          <h4>Frames</h4>
          <div className={styles.statGrid}>
            <div className={styles.stat}>
              <span className={styles.label}>Received</span>
              <span className={styles.value}>
                {stats.framesReceived.toLocaleString()}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.label}>Dropped</span>
              <span className={styles.value} style={{
                color: stats.framesDropped > 0 ? '#ff453a' : '#30d158'
              }}>
                {stats.framesDropped.toLocaleString()}
              </span>
            </div>
            <div className={styles.stat}>
              <span className={styles.label}>Drop Rate</span>
              <span className={styles.value}>
                {stats.framesReceived > 0 
                  ? formatNumber((stats.framesDropped / stats.framesReceived) * 100, 2)
                  : '0.0'
                }%
              </span>
            </div>
          </div>
        </div>

        {/* Rendering Performance */}
        {renderStats && (
          <div className={styles.section}>
            <h4>Rendering</h4>
            <div className={styles.statGrid}>
              <div className={styles.stat}>
                <span className={styles.label}>Frame Time</span>
                <span className={styles.value}>
                  {formatNumber(renderStats.averageRenderTime || 0)} ms
                </span>
              </div>
              <div className={styles.stat}>
                <span className={styles.label}>FPS</span>
                <span className={styles.value}>
                  {renderStats.averageRenderTime > 0 
                    ? formatNumber(1000 / renderStats.averageRenderTime)
                    : '0'
                  }
                </span>
              </div>
              <div className={styles.stat}>
                <span className={styles.label}>Dropped Frames</span>
                <span className={styles.value} style={{
                  color: (renderStats.droppedFrames || 0) > 0 ? '#ff453a' : '#30d158'
                }}>
                  {(renderStats.droppedFrames || 0).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Configuration Info */}
        {config && (
          <div className={styles.section}>
            <h4>Configuration</h4>
            <div className={styles.configGrid}>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>Sample Rate</span>
                <span className={styles.configValue}>
                  {(config.sps / 1e6).toFixed(1)} MHz
                </span>
              </div>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>FFT Size</span>
                <span className={styles.configValue}>
                  {config.fft_size.toLocaleString()}
                </span>
              </div>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>Bandwidth</span>
                <span className={styles.configValue}>
                  {(config.total_bandwidth / 1e6).toFixed(1)} MHz
                </span>
              </div>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>Base Freq</span>
                <span className={styles.configValue}>
                  {(config.basefreq / 1e6).toFixed(3)} MHz
                </span>
              </div>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>Compression</span>
                <span className={styles.configValue}>
                  {config.waterfall_compression.toUpperCase()}
                </span>
              </div>
              <div className={styles.configItem}>
                <span className={styles.configLabel}>Grid Locator</span>
                <span className={styles.configValue}>
                  {config.grid_locator || 'N/A'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Performance Indicators */}
        <div className={styles.section}>
          <h4>Performance</h4>
          <div className={styles.indicators}>
            <div className={styles.indicator}>
              <span className={styles.indicatorLabel}>Connection Quality</span>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ 
                    width: `${Math.min(100, (stats.dataRate / 1000) * 100)}%`,
                    backgroundColor: stats.dataRate > 500 ? '#30d158' : 
                                   stats.dataRate > 100 ? '#ff9f0a' : '#ff453a'
                  }}
                />
              </div>
            </div>
            
            <div className={styles.indicator}>
              <span className={styles.indicatorLabel}>Frame Rate</span>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ 
                    width: `${Math.min(100, ((renderStats?.averageRenderTime ? 1000 / renderStats.averageRenderTime : 0) / 60) * 100)}%`,
                    backgroundColor: (renderStats?.averageRenderTime ? 1000 / renderStats.averageRenderTime : 0) > 30 ? '#30d158' : 
                                   (renderStats?.averageRenderTime ? 1000 / renderStats.averageRenderTime : 0) > 15 ? '#ff9f0a' : '#ff453a'
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
