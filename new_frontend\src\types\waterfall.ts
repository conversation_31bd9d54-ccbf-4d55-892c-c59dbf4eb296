/**
 * PhantomSDR-Plus Waterfall Types
 * Complete type definitions for waterfall functionality
 */

// Core waterfall configuration from backend
export interface WaterfallConfig {
  sps: number;                    // Sample rate
  audio_max_sps: number;         // Maximum audio sample rate
  audio_max_fft: number;         // Maximum audio FFT size
  fft_size: number;              // FFT size
  fft_result_size: number;       // FFT result size
  waterfall_size: number;        // Waterfall size (min_waterfall_fft)
  basefreq: number;              // Base frequency
  total_bandwidth: number;       // Total bandwidth
  defaults: {
    frequency: number;
    modulation: string;
    l: number;                   // Left boundary
    m: number;                   // Center frequency
    r: number;                   // Right boundary
  };
  waterfall_compression: string; // "zstd" or "av1"
  audio_compression: string;     // "flac" or "opus"
  grid_locator: string;
  smeter_offset: number;
  markers: any;                  // Frequency markers
}

// Waterfall data packet structure
export interface WaterfallPacket {
  frame_num: number;             // Frame number for synchronization
  l: number;                     // Left frequency bin
  r: number;                     // Right frequency bin
  data: Uint8Array;              // Compressed waterfall data
}

// Decompressed waterfall data
export interface WaterfallData {
  frameNum: number;
  left: number;
  right: number;
  powerValues: Int8Array;        // Power values in dB
  timestamp: number;             // Client-side timestamp
}

// Window command for backend
export interface WindowCommand {
  cmd: 'window';
  l: number;                     // Left boundary
  r: number;                     // Right boundary
  m?: number;                    // Center frequency (optional)
  level?: number;                // Zoom level (optional)
}

// User ID command
export interface UserIdCommand {
  cmd: 'userid';
  userid: string;
}

// Mute command
export interface MuteCommand {
  cmd: 'mute';
  mute: boolean;
}

// Union type for all commands
export type WaterfallCommand = WindowCommand | UserIdCommand | MuteCommand;

// Waterfall display parameters
export interface WaterfallDisplayParams {
  width: number;                 // Canvas width in pixels
  height: number;                // Canvas height in pixels
  frequencyRange: {
    start: number;               // Start frequency in Hz
    end: number;                 // End frequency in Hz
  };
  timeRange: number;             // Time range in seconds
  colormap: string;              // Color scheme name
  brightness: number;            // Brightness adjustment (-100 to 100)
  contrast: number;              // Contrast adjustment (-100 to 100)
  zoom: {
    level: number;               // Zoom level (0-10)
    center: number;              // Center frequency for zoom
  };
}

// Waterfall rendering options
export interface WaterfallRenderOptions {
  smoothing: boolean;            // Enable smoothing
  interpolation: 'nearest' | 'linear' | 'cubic';
  refreshRate: number;           // Target refresh rate in FPS
  bufferSize: number;            // Number of lines to buffer
  enableGPU: boolean;            // Use GPU acceleration if available
}

// Color map definition
export interface ColorMap {
  name: string;
  colors: Array<{
    position: number;            // 0.0 to 1.0
    color: string;               // Hex color
  }>;
}

// Waterfall statistics
export interface WaterfallStats {
  framesReceived: number;
  framesDropped: number;
  dataRate: number;              // kbits/s
  compressionRatio: number;
  averageLatency: number;        // ms
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
}

// Event handlers
export interface WaterfallEventHandlers {
  onFrequencyChange?: (frequency: number) => void;
  onZoomChange?: (level: number, center: number) => void;
  onError?: (error: Error) => void;
  onConnect?: (config: WaterfallConfig) => void;
  onDisconnect?: () => void;
  onDataReceived?: (data: WaterfallData) => void;
}

// Waterfall component props
export interface WaterfallProps {
  websocketUrl: string;
  width?: number;
  height?: number;
  displayParams?: Partial<WaterfallDisplayParams>;
  renderOptions?: Partial<WaterfallRenderOptions>;
  eventHandlers?: WaterfallEventHandlers;
  className?: string;
  style?: React.CSSProperties;
}

// WebSocket connection state
export interface WebSocketState {
  readyState: number;
  url: string;
  protocol: string;
  extensions: string;
  bufferedAmount: number;
}

// Performance metrics
export interface PerformanceMetrics {
  renderTime: number;            // ms per frame
  decompressTime: number;        // ms per frame
  memoryUsage: number;           // MB
  cpuUsage: number;              // percentage
  gpuUsage?: number;             // percentage (if available)
}

// Error types
export enum WaterfallErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  DECOMPRESSION_FAILED = 'DECOMPRESSION_FAILED',
  RENDER_FAILED = 'RENDER_FAILED',
  INVALID_DATA = 'INVALID_DATA',
  WEBSOCKET_ERROR = 'WEBSOCKET_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'
}

export class WaterfallError extends Error {
  constructor(
    public type: WaterfallErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'WaterfallError';
  }
}
