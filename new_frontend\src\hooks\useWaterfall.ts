/**
 * React Hook for Waterfall Management
 * Provides a clean interface for waterfall functionality with React
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { 
  WaterfallConfig, 
  WaterfallData, 
  WaterfallStats, 
  WaterfallDisplayParams,
  WaterfallRenderOptions,
  WaterfallEventHandlers,
  WaterfallError
} from '@/types/waterfall';
import { WaterfallClient } from '@/lib/websocket/waterfall-client';
import { WaterfallRenderer } from '@/lib/rendering/waterfall-renderer';

export interface UseWaterfallOptions {
  websocketUrl: string;
  displayParams?: Partial<WaterfallDisplayParams>;
  renderOptions?: Partial<WaterfallRenderOptions>;
  autoConnect?: boolean;
  reconnectOnError?: boolean;
}

export interface UseWaterfallReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  config: WaterfallConfig | null;
  error: WaterfallError | null;
  
  // Statistics
  stats: WaterfallStats;
  
  // Canvas ref for rendering
  canvasRef: React.RefObject<HTMLCanvasElement>;
  
  // Control functions
  connect: () => Promise<void>;
  disconnect: () => void;
  setWindow: (left: number, right: number, center?: number, level?: number) => void;
  setUserId: (userId: string) => void;
  setMute: (mute: boolean) => void;
  
  // Display controls
  setBrightness: (brightness: number) => void;
  setContrast: (contrast: number) => void;
  setColorMap: (colorMapName: string) => void;
  updateDisplayParams: (params: Partial<WaterfallDisplayParams>) => void;
  updateRenderOptions: (options: Partial<WaterfallRenderOptions>) => void;
  
  // Utility functions
  clear: () => void;
  getRenderStats: () => any;
}

/**
 * Custom hook for managing waterfall functionality
 */
export function useWaterfall(options: UseWaterfallOptions): UseWaterfallReturn {
  const {
    websocketUrl,
    displayParams = {},
    renderOptions = {},
    autoConnect = true,
    reconnectOnError = true,
  } = options;

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [config, setConfig] = useState<WaterfallConfig | null>(null);
  const [error, setError] = useState<WaterfallError | null>(null);
  const [stats, setStats] = useState<WaterfallStats>({
    framesReceived: 0,
    framesDropped: 0,
    dataRate: 0,
    compressionRatio: 0,
    averageLatency: 0,
    connectionStatus: 'disconnected',
  });

  // Refs
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const clientRef = useRef<WaterfallClient | null>(null);
  const rendererRef = useRef<WaterfallRenderer | null>(null);
  const mountedRef = useRef(true);

  // Default display parameters
  const defaultDisplayParams: WaterfallDisplayParams = {
    width: 1024,
    height: 400,
    frequencyRange: { start: 0, end: 30000000 },
    timeRange: 60,
    colormap: 'apple',
    brightness: 0,
    contrast: 0,
    zoom: { level: 0, center: 15000000 },
    ...displayParams,
  };

  // Event handlers
  const eventHandlers: WaterfallEventHandlers = {
    onConnect: useCallback((receivedConfig: WaterfallConfig) => {
      if (!mountedRef.current) return;
      setConfig(receivedConfig);
      setIsConnected(true);
      setIsConnecting(false);
      setError(null);
    }, []),

    onDisconnect: useCallback(() => {
      if (!mountedRef.current) return;
      setIsConnected(false);
      setIsConnecting(false);
    }, []),

    onData: useCallback((data: WaterfallData) => {
      if (!mountedRef.current) return;
      rendererRef.current?.addData(data);
    }, []),

    onError: useCallback((waterfallError: WaterfallError) => {
      if (!mountedRef.current) return;
      setError(waterfallError);
      setIsConnecting(false);
      
      if (reconnectOnError && waterfallError.type === 'WEBSOCKET_ERROR') {
        // Attempt reconnection after a delay
        setTimeout(() => {
          if (mountedRef.current) {
            connect();
          }
        }, 5000);
      }
    }, [reconnectOnError]),

    onStatsUpdate: useCallback((updatedStats: WaterfallStats) => {
      if (!mountedRef.current) return;
      setStats(updatedStats);
    }, []),
  };

  // Initialize renderer when canvas is available
  useEffect(() => {
    if (canvasRef.current && !rendererRef.current) {
      try {
        rendererRef.current = new WaterfallRenderer(
          canvasRef.current,
          defaultDisplayParams,
          renderOptions
        );
      } catch (err) {
        console.error('Failed to initialize waterfall renderer:', err);
        setError(new WaterfallError('RENDER_FAILED', 'Failed to initialize renderer', err));
      }
    }

    return () => {
      if (rendererRef.current) {
        rendererRef.current.destroy();
        rendererRef.current = null;
      }
    };
  }, [canvasRef.current]);

  // Connect function
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) return;

    try {
      setIsConnecting(true);
      setError(null);

      // Create client if not exists
      if (!clientRef.current) {
        clientRef.current = new WaterfallClient(websocketUrl, eventHandlers);
      }

      await clientRef.current.connect();
    } catch (err) {
      if (mountedRef.current) {
        setIsConnecting(false);
        const waterfallError = err instanceof WaterfallError 
          ? err 
          : new WaterfallError('CONNECTION_FAILED', 'Failed to connect', err);
        setError(waterfallError);
      }
    }
  }, [websocketUrl, isConnecting, isConnected, eventHandlers]);

  // Disconnect function
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
    }
    setIsConnected(false);
    setIsConnecting(false);
  }, []);

  // Control functions
  const setWindow = useCallback((left: number, right: number, center?: number, level?: number) => {
    clientRef.current?.setWindow(left, right, center, level);
  }, []);

  const setUserId = useCallback((userId: string) => {
    clientRef.current?.setUserId(userId);
  }, []);

  const setMute = useCallback((mute: boolean) => {
    clientRef.current?.setMute(mute);
  }, []);

  // Display control functions
  const setBrightness = useCallback((brightness: number) => {
    rendererRef.current?.setBrightness(brightness);
  }, []);

  const setContrast = useCallback((contrast: number) => {
    rendererRef.current?.setContrast(contrast);
  }, []);

  const setColorMap = useCallback((colorMapName: string) => {
    rendererRef.current?.setColorMap(colorMapName);
  }, []);

  const updateDisplayParams = useCallback((params: Partial<WaterfallDisplayParams>) => {
    rendererRef.current?.updateDisplayParams(params);
  }, []);

  const updateRenderOptions = useCallback((options: Partial<WaterfallRenderOptions>) => {
    rendererRef.current?.updateRenderOptions(options);
  }, []);

  // Utility functions
  const clear = useCallback(() => {
    rendererRef.current?.clear();
  }, []);

  const getRenderStats = useCallback(() => {
    return rendererRef.current?.getRenderStats() || {};
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      mountedRef.current = false;
      disconnect();
      if (clientRef.current) {
        clientRef.current.destroy();
        clientRef.current = null;
      }
    };
  }, [autoConnect, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    // Connection state
    isConnected,
    isConnecting,
    config,
    error,
    
    // Statistics
    stats,
    
    // Canvas ref
    canvasRef,
    
    // Control functions
    connect,
    disconnect,
    setWindow,
    setUserId,
    setMute,
    
    // Display controls
    setBrightness,
    setContrast,
    setColorMap,
    updateDisplayParams,
    updateRenderOptions,
    
    // Utility functions
    clear,
    getRenderStats,
  };
}
