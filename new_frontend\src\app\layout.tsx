import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'PhantomSDR-Plus Waterfall',
  description: 'Real-time spectrum waterfall display for PhantomSDR-Plus',
  keywords: ['SDR', 'waterfall', 'spectrum', 'radio', 'PhantomSDR'],
  authors: [{ name: 'PhantomSDR-Plus Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#007aff',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      </head>
      <body>{children}</body>
    </html>
  );
}
