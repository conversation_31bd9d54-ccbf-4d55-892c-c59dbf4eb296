/**
 * Global Styles
 * Apple-inspired design system with modern CSS features
 */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
               'Helvetica Neue', Arial, sans-serif;
  background: #1c1c1e;
  color: #ffffff;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

p {
  line-height: 1.6;
}

/* Focus Styles */
:focus {
  outline: 2px solid #007aff;
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Selection Styles */
::selection {
  background: rgba(0, 122, 255, 0.3);
  color: inherit;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Form Elements */
input, select, textarea, button {
  font-family: inherit;
  font-size: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
}

/* Links */
a {
  color: #007aff;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #0056cc;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.no-scroll {
  overflow: hidden;
}

/* Animation Utilities */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Dark Mode (Default) */
@media (prefers-color-scheme: dark) {
  body {
    background: #1c1c1e;
    color: #ffffff;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  body {
    background: #ffffff;
    color: #000000;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
  
  ::selection {
    background: rgba(0, 122, 255, 0.2);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :focus {
    outline-width: 3px;
  }
  
  a {
    text-decoration: underline;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-down,
  .animate-scale-in {
    animation: none;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
    color: black;
  }
  
  .no-print {
    display: none !important;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  body {
    -webkit-overflow-scrolling: touch;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  button,
  [role="button"],
  input[type="submit"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Loading States */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.error {
  color: #ff453a;
}

.error-border {
  border-color: #ff453a !important;
}

/* Success States */
.success {
  color: #30d158;
}

.success-border {
  border-color: #30d158 !important;
}

/* Warning States */
.warning {
  color: #ff9f0a;
}

.warning-border {
  border-color: #ff9f0a !important;
}
