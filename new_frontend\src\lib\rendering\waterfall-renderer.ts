/**
 * High-Performance Waterfall Renderer
 * Optimized for 60fps rendering with Apple-quality smoothness
 */

import { WaterfallData, WaterfallDisplayParams, WaterfallRenderOptions } from '@/types/waterfall';
import { ColorMapper } from './colormap';

/**
 * Circular buffer for efficient waterfall line storage
 */
class WaterfallBuffer {
  private buffer: Array<WaterfallData | null>;
  private head = 0;
  private size = 0;
  private readonly capacity: number;

  constructor(capacity: number) {
    this.capacity = capacity;
    this.buffer = new Array(capacity).fill(null);
  }

  /**
   * Add new waterfall line
   */
  push(data: WaterfallData): void {
    this.buffer[this.head] = data;
    this.head = (this.head + 1) % this.capacity;
    this.size = Math.min(this.size + 1, this.capacity);
  }

  /**
   * Get line at index (0 = oldest, size-1 = newest)
   */
  get(index: number): WaterfallData | null {
    if (index >= this.size) return null;
    const bufferIndex = (this.head - this.size + index + this.capacity) % this.capacity;
    return this.buffer[bufferIndex];
  }

  /**
   * Get current size
   */
  getSize(): number {
    return this.size;
  }

  /**
   * Clear buffer
   */
  clear(): void {
    this.buffer.fill(null);
    this.head = 0;
    this.size = 0;
  }
}

/**
 * High-performance waterfall renderer with GPU acceleration
 */
export class WaterfallRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private offscreenCanvas: OffscreenCanvas | null = null;
  private offscreenCtx: OffscreenCanvasRenderingContext2D | null = null;
  private colorMapper: ColorMapper;
  private buffer: WaterfallBuffer;
  private displayParams: WaterfallDisplayParams;
  private renderOptions: WaterfallRenderOptions;
  private animationFrameId: number | null = null;
  private lastRenderTime = 0;
  private renderStats = {
    frameCount: 0,
    averageRenderTime: 0,
    droppedFrames: 0,
  };

  // Rendering buffers
  private imageData: ImageData | null = null;
  private colorBuffer: Uint8ClampedArray | null = null;

  constructor(
    canvas: HTMLCanvasElement,
    displayParams: WaterfallDisplayParams,
    renderOptions: WaterfallRenderOptions = {}
  ) {
    this.canvas = canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Failed to get 2D rendering context');
    }
    this.ctx = ctx;

    this.displayParams = displayParams;
    this.renderOptions = {
      smoothing: true,
      interpolation: 'linear',
      refreshRate: 60,
      bufferSize: 1000,
      enableGPU: true,
      ...renderOptions,
    };

    this.colorMapper = new ColorMapper();
    this.buffer = new WaterfallBuffer(this.renderOptions.bufferSize!);

    this.initializeOffscreenCanvas();
    this.setupCanvas();
  }

  /**
   * Initialize offscreen canvas for background rendering
   */
  private initializeOffscreenCanvas(): void {
    if (typeof OffscreenCanvas !== 'undefined' && this.renderOptions.enableGPU) {
      try {
        this.offscreenCanvas = new OffscreenCanvas(
          this.displayParams.width,
          this.displayParams.height
        );
        this.offscreenCtx = this.offscreenCanvas.getContext('2d');
      } catch (error) {
        console.warn('OffscreenCanvas not available, using main canvas');
      }
    }
  }

  /**
   * Setup canvas properties
   */
  private setupCanvas(): void {
    // Set canvas size
    this.canvas.width = this.displayParams.width;
    this.canvas.height = this.displayParams.height;

    // Configure rendering context
    this.ctx.imageSmoothingEnabled = this.renderOptions.smoothing!;
    this.ctx.imageSmoothingQuality = 'high';

    // Initialize image data
    this.imageData = this.ctx.createImageData(
      this.displayParams.width,
      this.displayParams.height
    );
    this.colorBuffer = new Uint8ClampedArray(
      this.displayParams.width * this.displayParams.height * 4
    );
  }

  /**
   * Add new waterfall data
   */
  addData(data: WaterfallData): void {
    this.buffer.push(data);
    
    // Start rendering if not already running
    if (!this.animationFrameId) {
      this.startRendering();
    }
  }

  /**
   * Start the rendering loop
   */
  private startRendering(): void {
    const targetFrameTime = 1000 / this.renderOptions.refreshRate!;
    
    const render = (timestamp: number) => {
      const deltaTime = timestamp - this.lastRenderTime;
      
      if (deltaTime >= targetFrameTime) {
        const renderStartTime = performance.now();
        this.renderFrame();
        const renderTime = performance.now() - renderStartTime;
        
        this.updateRenderStats(renderTime);
        this.lastRenderTime = timestamp;
      }
      
      this.animationFrameId = requestAnimationFrame(render);
    };
    
    this.animationFrameId = requestAnimationFrame(render);
  }

  /**
   * Stop the rendering loop
   */
  stopRendering(): void {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Render a single frame
   */
  private renderFrame(): void {
    if (!this.imageData || !this.colorBuffer) return;

    const bufferSize = this.buffer.getSize();
    if (bufferSize === 0) return;

    // Clear the image data
    this.colorBuffer.fill(0);

    // Calculate how many lines to render
    const linesToRender = Math.min(bufferSize, this.displayParams.height);
    const startLine = Math.max(0, bufferSize - linesToRender);

    // Render each line
    for (let y = 0; y < linesToRender; y++) {
      const dataIndex = startLine + y;
      const data = this.buffer.get(dataIndex);
      
      if (data) {
        this.renderLine(data, linesToRender - 1 - y); // Newest at top
      }
    }

    // Copy to image data and render
    this.imageData.data.set(this.colorBuffer);
    
    // Use offscreen canvas if available
    const renderCtx = this.offscreenCtx || this.ctx;
    const renderCanvas = this.offscreenCanvas || this.canvas;
    
    renderCtx.putImageData(this.imageData, 0, 0);
    
    // Copy from offscreen to main canvas if using offscreen rendering
    if (this.offscreenCanvas && this.offscreenCtx) {
      this.ctx.drawImage(this.offscreenCanvas, 0, 0);
    }
  }

  /**
   * Render a single waterfall line
   */
  private renderLine(data: WaterfallData, y: number): void {
    if (!this.colorBuffer) return;

    const { powerValues, left, right } = data;
    const lineWidth = this.displayParams.width;
    const lineOffset = y * lineWidth * 4;

    // Calculate frequency mapping
    const freqStart = this.displayParams.frequencyRange.start;
    const freqEnd = this.displayParams.frequencyRange.end;
    const freqRange = freqEnd - freqStart;
    
    // Map power values to pixels
    for (let x = 0; x < lineWidth; x++) {
      const freq = freqStart + (x / lineWidth) * freqRange;
      
      // Find corresponding power value
      const binIndex = this.frequencyToBin(freq, left, right, powerValues.length);
      const powerValue = this.interpolatePowerValue(powerValues, binIndex);
      
      // Map to color
      const pixelOffset = lineOffset + x * 4;
      this.mapPowerToColor(powerValue, pixelOffset);
    }
  }

  /**
   * Convert frequency to bin index
   */
  private frequencyToBin(freq: number, left: number, right: number, binCount: number): number {
    const binRange = right - left;
    const normalizedFreq = (freq - left) / binRange;
    return normalizedFreq * binCount;
  }

  /**
   * Interpolate power value at fractional bin index
   */
  private interpolatePowerValue(powerValues: Int8Array, binIndex: number): number {
    if (binIndex < 0 || binIndex >= powerValues.length) {
      return -128; // Minimum power value
    }

    if (this.renderOptions.interpolation === 'nearest') {
      return powerValues[Math.round(binIndex)];
    }

    // Linear interpolation
    const lowerIndex = Math.floor(binIndex);
    const upperIndex = Math.ceil(binIndex);
    
    if (lowerIndex === upperIndex) {
      return powerValues[lowerIndex];
    }

    const fraction = binIndex - lowerIndex;
    const lowerValue = powerValues[lowerIndex] || -128;
    const upperValue = powerValues[upperIndex] || -128;
    
    return lowerValue + (upperValue - lowerValue) * fraction;
  }

  /**
   * Map power value to color in buffer
   */
  private mapPowerToColor(powerValue: number, pixelOffset: number): void {
    if (!this.colorBuffer) return;

    // Create temporary array for single pixel
    const tempPower = new Int8Array([powerValue]);
    const tempColor = new Uint8ClampedArray(4);
    
    this.colorMapper.mapColors(tempPower, tempColor);
    
    // Copy to main buffer
    this.colorBuffer[pixelOffset] = tempColor[0];     // Red
    this.colorBuffer[pixelOffset + 1] = tempColor[1]; // Green
    this.colorBuffer[pixelOffset + 2] = tempColor[2]; // Blue
    this.colorBuffer[pixelOffset + 3] = tempColor[3]; // Alpha
  }

  /**
   * Update display parameters
   */
  updateDisplayParams(params: Partial<WaterfallDisplayParams>): void {
    this.displayParams = { ...this.displayParams, ...params };
    
    // Resize if dimensions changed
    if (params.width || params.height) {
      this.setupCanvas();
    }
  }

  /**
   * Update render options
   */
  updateRenderOptions(options: Partial<WaterfallRenderOptions>): void {
    this.renderOptions = { ...this.renderOptions, ...options };
    
    // Update buffer size if changed
    if (options.bufferSize) {
      this.buffer = new WaterfallBuffer(options.bufferSize);
    }
    
    // Update canvas smoothing
    if (options.smoothing !== undefined) {
      this.ctx.imageSmoothingEnabled = options.smoothing;
    }
  }

  /**
   * Set color map
   */
  setColorMap(colorMapName: string): void {
    // This would be implemented to use the ColorMapper
    // For now, just update brightness/contrast
  }

  /**
   * Set brightness
   */
  setBrightness(brightness: number): void {
    this.colorMapper.setBrightness(brightness);
  }

  /**
   * Set contrast
   */
  setContrast(contrast: number): void {
    this.colorMapper.setContrast(contrast);
  }

  /**
   * Update render statistics
   */
  private updateRenderStats(renderTime: number): void {
    this.renderStats.frameCount++;
    this.renderStats.averageRenderTime = 
      (this.renderStats.averageRenderTime + renderTime) / 2;
    
    if (renderTime > 16.67) { // Dropped frame at 60fps
      this.renderStats.droppedFrames++;
    }
  }

  /**
   * Get render statistics
   */
  getRenderStats(): typeof this.renderStats {
    return { ...this.renderStats };
  }

  /**
   * Clear the waterfall display
   */
  clear(): void {
    this.buffer.clear();
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
  }

  /**
   * Destroy the renderer and clean up resources
   */
  destroy(): void {
    this.stopRendering();
    this.buffer.clear();
    this.imageData = null;
    this.colorBuffer = null;
    this.offscreenCanvas = null;
    this.offscreenCtx = null;
  }
}
